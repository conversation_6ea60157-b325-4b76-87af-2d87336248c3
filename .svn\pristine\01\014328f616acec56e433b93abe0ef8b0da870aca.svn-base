using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace DianQiXianLu {
    public class CircuitryController : MonoBehaviour {
        [HideInInspector] public ConnectPort[] ports;
        [HideInInspector] public ConnectLine[] lines;
        private List<ConnectPort> selectedPorts;

        public void Startup(int circuitryType) {
            gameObject.SetActive(true);

            selectedPorts = new List<ConnectPort> {
                Capacity = 2
            };

            ports = GetComponentsInChildren<ConnectPort>();
            lines = GetComponentsInChildren<ConnectLine>();

            foreach (var port in ports) {
                port.onPortSelected += OnPortSelected;
                port.onPortDeSelected += OnPortDeSelected;
            }

            if (circuitryType == 0) {
                foreach (var line in lines) {
                    if (line.isControlCircuitry)
                        line.SetConnect();
                }
            }
            else if (circuitryType == 1) {
                foreach (var line in lines) {
                    if (!line.isControlCircuitry)
                        line.SetConnect();
                }
            }
        }

        public void Exit() {
            foreach (var port in ports) {
                port.onPortSelected -= OnPortSelected;
                port.onPortDeSelected -= OnPortDeSelected;
            }

            gameObject.SetActive(false);
        }

        private void OnPortSelected(ConnectPort port) {
            if (selectedPorts.Count < 2) {
                selectedPorts.Add(port);

                if (selectedPorts.Count == 2) {
                    var isConnect = FindLine(selectedPorts[0], selectedPorts[1], out var line);

                    if (isConnect)
                        OnCorrect(line);
                    else
                        OnWrong();
                }
            }
        }

        private void OnPortDeSelected(ConnectPort port) {
            if (selectedPorts.Contains(port)) selectedPorts.Remove(port);
        }

        private bool FindLine(ConnectPort selectedPort, ConnectPort connectPort, out ConnectLine line) {
            line = lines.FirstOrDefault(l => (l.port1 == selectedPort && l.port2 == connectPort) || (l.port1 == connectPort && l.port2 == selectedPort));

            return line != null;
        }


        private void OnCorrect(ConnectLine line) {
            selectedPorts[0].SetCorrect();
            selectedPorts[1].SetCorrect();
            selectedPorts.Clear();
            line.SetConnect();

            if (CircuitryManager.Instance.isHelperOn) HelpNextConnection();

            AudioManager.Instance.Play2DAudio(AudioManager.Audio2D.Correct);
        }

        private void OnWrong() {
            selectedPorts[0].SetWrong();
            selectedPorts[1].SetWrong();
            selectedPorts.Clear();

            CircuitryManager.Instance.OnWrong();

            AudioManager.Instance.Play2DAudio(AudioManager.Audio2D.Wrong);
        }

        public void HelpNextConnection() {
            var next = lines.FirstOrDefault(line => !line.isConnected);
            if (next == null) {
                onConnectComplete?.Invoke();
                return;
            }

            next.EnableHighlight();
        }


        public event Action onConnectComplete;
    }
}