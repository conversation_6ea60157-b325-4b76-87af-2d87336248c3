// Created by Microsoft (R) C/C++ Compiler Version 14.13.26128.0 (73c17aba).
//
// c:\projects\unity1\artifacts\editorapp\win64_debug_vs2017\dte80a.tlh
//
// C++ source equivalent of Win32 type library 80cc9f66-e7d8-4ddd-85b6-d9e6cd0e93e2
// compiler-generated file created 05/30/18 at 11:33:08 - DO NOT EDIT!

#pragma once
#pragma pack(push, 8)

#include <comdef.h>

namespace EnvDTE {

	//
	// Forward references and typedefs
	//

	struct __declspec(uuid("80cc9f66-e7d8-4ddd-85b6-d9e6cd0e93e2"))
		/* LIBID */ __EnvDTE;
	struct __declspec(uuid("04a72314-32e9-48e2-9b87-a63603454f3e"))
		/* dual interface */ _DTE;
	struct __declspec(uuid("2294311a-b7bc-4789-b365-1c15ff2cd17c"))
		/* dual interface */ Windows;
	struct __declspec(uuid("0beab46b-4c07-4f94-a8d7-1626020e4e53"))
		/* dual interface */ Window;
	enum vsWindowState;
	enum vsWindowType;
	struct __declspec(uuid("f00ef34a-a654-4c1b-897a-585d5bcbb35a"))
		/* dual interface */ LinkedWindows;
	struct /* coclass */ DTE;
	struct __declspec(uuid("0b48100a-473e-433c-ab8f-66b9739ab620"))
		/* dual interface */ ProjectItem;
	struct __declspec(uuid("8e2f1269-185e-43c7-8899-950ad2769ccf"))
		/* dual interface */ ProjectItems;
	struct __declspec(uuid("866311e6-c887-4143-9833-645f5b93f6f1"))
		/* dual interface */ Project;
	struct __declspec(uuid("e3ec0add-31b3-461f-8303-8a5e6931257a"))
		/* dual interface */ Projects;
	struct __declspec(uuid("4cc8ccf5-a926-4646-b17f-b4940caed472"))
		/* dual interface */ Properties;
	struct __declspec(uuid("7b988e06-2581-485e-9322-04881e0600d0"))
		/* dual interface */ Property;
	struct __declspec(uuid("9043fda1-345b-4364-900f-bc8598eb8e4f"))
		/* dual interface */ ConfigurationManager;
	struct __declspec(uuid("90813589-fe21-4aa4-a2e5-053fd274e980"))
		/* dual interface */ Configuration;
	enum vsConfigurationType;
	struct __declspec(uuid("f9fa748e-e302-44cf-891b-e263189d585e"))
		/* dual interface */ OutputGroups;
	struct __declspec(uuid("a3a80783-875f-435b-9639-e5ce888df737"))
		/* dual interface */ OutputGroup;
	struct __declspec(uuid("b6b4c8d6-4d27-43b9-b45c-52bd16b6ba38"))
		/* dual interface */ Configurations;
	struct __declspec(uuid("e68a3e0e-b435-4dde-86b7-f5adefc19df2"))
		/* dual interface */ Globals;
	struct __declspec(uuid("0cfbc2b4-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeModel;
	struct __declspec(uuid("0cfbc2b5-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeElements;
	struct __declspec(uuid("0cfbc2b6-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeElement;
	enum vsCMElement;
	enum vsCMInfoLocation;
	struct __declspec(uuid("7f59e94e-4939-40d2-9f7f-b7651c25905d"))
		/* dual interface */ TextPoint;
	struct __declspec(uuid("cb218890-1382-472b-9118-782700c88115"))
		/* dual interface */ TextDocument;
	struct __declspec(uuid("63eb5c39-ca8f-498e-9a66-6dd4a27ac95b"))
		/* dual interface */ Document;
	struct __declspec(uuid("9e2cf3ea-140f-413e-bd4b-7d46740cd2f4"))
		/* dual interface */ Documents;
	enum vsSaveChanges;
	enum vsSaveStatus;
	struct __declspec(uuid("1fa0e135-399a-4d2c-a4fe-d21e2480f921"))
		/* dual interface */ TextSelection;
	struct __declspec(uuid("42320454-626c-4dd0-9ecb-357c4f1966d8"))
		/* dual interface */ VirtualPoint;
	enum vsCaseOptions;
	enum vsWhitespaceOptions;
	enum vsStartOfLineOptions;
	struct __declspec(uuid("b6422e9c-9efd-4f87-bddc-c7fd8f2fd303"))
		/* dual interface */ TextRanges;
	struct __declspec(uuid("72767524-e3b3-43d0-bb46-bbe1d556a9ff"))
		/* dual interface */ TextRange;
	struct __declspec(uuid("c1ffe800-028b-4475-a907-14f51f19bb7d"))
		/* dual interface */ EditPoint;
	struct __declspec(uuid("0a3bf283-05f8-4669-9bcb-a84b6423349a"))
		/* dual interface */ TextPane;
	struct __declspec(uuid("d9013d31-3652-46b2-a25a-29a881b9f86b"))
		/* dual interface */ TextPanes;
	struct __declspec(uuid("2fc54dc9-922b-44eb-8cc0-ba182584dc4b"))
		/* dual interface */ TextWindow;
	enum vsPaneShowHow;
	enum vsSelectionMode;
	enum vsCMPart;
	struct __declspec(uuid("0cfbc2b7-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeType;
	struct __declspec(uuid("0cfbc2b8-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeNamespace;
	enum vsCMAccess;
	struct __declspec(uuid("b1f42514-91cd-4d3a-8b25-a317d8032b24"))
		/* dual interface */ CodeClass;
	struct __declspec(uuid("0cfbc2be-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeAttribute;
	struct __declspec(uuid("b1f42510-91cd-4d3a-8b25-a317d8032b24"))
		/* dual interface */ CodeInterface;
	enum vsCMFunction;
	struct __declspec(uuid("0cfbc2b9-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeFunction;
	struct __declspec(uuid("0cfbc2bc-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeTypeRef;
	enum vsCMTypeRef;
	struct __declspec(uuid("0cfbc2bd-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeParameter;
	struct __declspec(uuid("0cfbc2bb-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeProperty;
	struct __declspec(uuid("0cfbc2ba-0d4e-11d3-8997-00c04f688dde"))
		/* dual interface */ CodeVariable;
	struct __declspec(uuid("b1f42511-91cd-4d3a-8b25-a317d8032b24"))
		/* dual interface */ CodeStruct;
	struct __declspec(uuid("b1f42512-91cd-4d3a-8b25-a317d8032b24"))
		/* dual interface */ CodeEnum;
	struct __declspec(uuid("b1f42513-91cd-4d3a-8b25-a317d8032b24"))
		/* dual interface */ CodeDelegate;
	struct __declspec(uuid("ed1a3f99-4477-11d3-89bf-00c04f688dde"))
		/* dual interface */ FileCodeModel;
	struct __declspec(uuid("33c5ebb8-244e-449d-9cee-fad70a774e59"))
		/* dual interface */ ContextAttributes;
	struct __declspec(uuid("1a6e2cb3-b897-42eb-96be-ff0fdb65db2f"))
		/* dual interface */ ContextAttribute;
	enum vsContextAttributeType;
	enum vsContextAttributes;
	struct __declspec(uuid("53a87fa1-ce93-48bf-958b-c6da793c5028"))
		/* dual interface */ AddIn;
	struct __declspec(uuid("50590801-d13e-4404-80c2-5ca30a4d0ee8"))
		/* dual interface */ AddIns;
	enum vsLinkedWindowType;
	struct __declspec(uuid("134170f8-93b1-42dd-9f89-a2ac7010ba07"))
		/* dual interface */ Events;
	struct /* coclass */ CommandEvents;
	struct __declspec(uuid("a79fc678-0d0a-496a-b9dc-0d5b9e1ca9fc"))
		/* interface */ _CommandEvents;
	struct __declspec(uuid("ff2d5c12-feea-11d0-bbc8-00a0c90f2744"))
		/* dispinterface */ _dispCommandEvents;
	struct /* coclass */ SelectionEvents;
	struct __declspec(uuid("eb6783db-1819-496d-84a4-3cff883010f6"))
		/* interface */ _SelectionEvents;
	struct __declspec(uuid("deeb28b3-23e6-11d1-ae5c-00a0c90f26f4"))
		/* dispinterface */ _dispSelectionEvents;
	struct /* coclass */ SolutionEvents;
	struct __declspec(uuid("bf8bbf37-5415-46a9-940d-594cad9dec26"))
		/* interface */ _SolutionEvents;
	struct __declspec(uuid("fbcff1c2-261c-11d1-ae5e-00a0c90f26f4"))
		/* dispinterface */ _dispSolutionEvents;
	struct /* coclass */ BuildEvents;
	struct __declspec(uuid("794a2ba5-ffa6-4fc5-bf13-957b2c22edd7"))
		/* interface */ _BuildEvents;
	struct __declspec(uuid("1926364e-6b90-46cb-a44d-8a80fb11acd9"))
		/* dispinterface */ _dispBuildEvents;
	enum vsBuildScope;
	enum vsBuildAction;
	struct /* coclass */ WindowEvents;
	struct __declspec(uuid("0d3a23a8-67bb-11d2-97c1-00c04fb6c6ff"))
		/* interface */ _WindowEvents;
	struct __declspec(uuid("0d3a23a9-67bb-11d2-97c1-00c04fb6c6ff"))
		/* dispinterface */ _dispWindowEvents;
	struct /* coclass */ OutputWindowEvents;
	struct __declspec(uuid("0a3546a8-6840-11d2-97c1-00c04fb6c6ff"))
		/* interface */ _OutputWindowEvents;
	struct __declspec(uuid("0d3a23af-67bb-11d2-97c1-00c04fb6c6ff"))
		/* dispinterface */ _dispOutputWindowEvents;
	struct __declspec(uuid("ffc9dfc4-61ca-4b0c-83c2-0703a24f5c16"))
		/* dual interface */ OutputWindowPane;
	struct __declspec(uuid("b02cf62a-9470-4308-a521-9675fba395ab"))
		/* dual interface */ OutputWindowPanes;
	struct __declspec(uuid("eab0a63d-c3a8-496e-9acf-a82cef6a43b3"))
		/* dual interface */ OutputWindow;
	enum vsTaskPriority;
	enum vsTaskIcon;
	struct /* coclass */ FindEvents;
	struct __declspec(uuid("c5331acd-c5d5-11d2-8598-006097c68e81"))
		/* interface */ _FindEvents;
	struct __declspec(uuid("c5331ace-c5d5-11d2-8598-006097c68e81"))
		/* dispinterface */ _dispFindEvents;
	enum vsFindResult;
	struct /* coclass */ TaskListEvents;
	struct __declspec(uuid("1125c422-49bd-11d2-8823-00c04fb6c6ff"))
		/* interface */ _TaskListEvents;
	struct __declspec(uuid("1125c423-49bd-11d2-8823-00c04fb6c6ff"))
		/* dispinterface */ _dispTaskListEvents;
	struct __declspec(uuid("58e4d419-6b8c-4c63-92de-70161cd95890"))
		/* dual interface */ TaskItem;
	struct __declspec(uuid("4b51103d-513c-4773-b56a-354d0928fd04"))
		/* dual interface */ TaskItems;
	struct __declspec(uuid("4e4f0569-e16a-4da1-92de-10882a4ddd8c"))
		/* dual interface */ TaskList;
	enum vsTaskListColumn;
	struct /* coclass */ DTEEvents;
	struct __declspec(uuid("fa1bb6d7-ca83-11d2-aab2-00c04f688dde"))
		/* interface */ _DTEEvents;
	struct __declspec(uuid("b50c9708-c909-4b87-a03d-af6cc4bfb422"))
		/* dispinterface */ _dispDTEEvents;
	enum vsIDEMode;
	struct /* coclass */ DocumentEvents;
	struct __declspec(uuid("dc5437f5-f114-11d2-aacf-00c04f688dde"))
		/* interface */ _DocumentEvents;
	struct __declspec(uuid("dc5437f6-f114-11d2-aacf-00c04f688dde"))
		/* dispinterface */ _dispDocumentEvents;
	struct /* coclass */ ProjectItemsEvents;
	struct __declspec(uuid("*************-410d-bf87-a7808eac977d"))
		/* dual interface */ _ProjectItemsEvents;
	struct __declspec(uuid("6962753f-efd5-41c5-b083-d70687166aeb"))
		/* dispinterface */ _dispProjectItemsEvents;
	struct /* coclass */ DebuggerEvents;
	struct __declspec(uuid("d4eae958-0fba-11d3-b880-00c04f79e479"))
		/* interface */ _DebuggerEvents;
	struct __declspec(uuid("46209330-0fba-11d3-b880-00c04f79e479"))
		/* dispinterface */ _dispDebuggerEvents;
	enum dbgEventReason;
	enum dbgExecutionAction;
	enum dbgExceptionAction;
	struct __declspec(uuid("5c5a0070-f396-4e37-a82a-1b767e272df9"))
		/* dual interface */ Process;
	struct __declspec(uuid("dc6a118a-bbab-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Programs;
	struct __declspec(uuid("6a38d87c-bba0-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Program;
	struct __declspec(uuid("6aa23fb4-bba1-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Threads;
	struct __declspec(uuid("9407f466-bba1-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Thread;
	struct __declspec(uuid("4ed85664-bba2-11d2-8ad1-00c04f79e479"))
		/* dual interface */ StackFrames;
	struct __declspec(uuid("1342d0d8-bba3-11d2-8ad1-00c04f79e479"))
		/* dual interface */ StackFrame;
	struct __declspec(uuid("2685337a-bb9e-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Expressions;
	struct __declspec(uuid("27adc812-bb07-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Expression;
	struct __declspec(uuid("338fb9a0-bae5-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Debugger;
	struct __declspec(uuid("25968106-bafb-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Breakpoints;
	struct __declspec(uuid("11c5114c-bb00-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Breakpoint;
	enum dbgBreakpointType;
	enum dbgBreakpointLocationType;
	enum dbgBreakpointConditionType;
	enum dbgHitCountType;
	struct __declspec(uuid("a4f4246c-c131-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Languages;
	struct __declspec(uuid("b3ccfa68-c145-11d2-8ad1-00c04f79e479"))
		/* dual interface */ Language;
	enum dbgDebugMode;
	struct __declspec(uuid("9f379969-5eac-4a54-b2bc-6946cffb56ef"))
		/* dual interface */ Processes;
	struct /* coclass */ TextEditorEvents;
	struct __declspec(uuid("23b7a868-6c89-436a-94fa-25d755456a77"))
		/* interface */ _TextEditorEvents;
	struct __declspec(uuid("2699dd44-c507-4da3-aa34-314a6c21dfe2"))
		/* dispinterface */ _dispTextEditorEvents;
	enum vsDisplay;
	struct /* coclass */ Solution;
	struct __declspec(uuid("26f6cc4b-7a48-4e4d-8af5-9e960232e05f"))
		/* dual interface */ _Solution;
	struct __declspec(uuid("a3c1c40c-9218-4d4c-9daa-075f64f6922c"))
		/* dual interface */ SolutionBuild;
	struct __declspec(uuid("60aaad75-cb8d-4c62-9959-24d6a6a50de7"))
		/* dual interface */ SolutionConfiguration;
	struct __declspec(uuid("23e78ed7-c9e1-462d-8bc6-366003486ed9"))
		/* dual interface */ SolutionConfigurations;
	struct __declspec(uuid("0685b546-fb84-4917-ab98-98d40f892d61"))
		/* dual interface */ SolutionContexts;
	struct __declspec(uuid("fc6a1a82-9c8a-47bb-a046-6e965df5a99b"))
		/* dual interface */ SolutionContext;
	struct __declspec(uuid("ead260eb-1e5b-450a-b628-4cfada11b4a1"))
		/* dual interface */ BuildDependencies;
	struct __declspec(uuid("9c5ceaac-062f-4434-a2ed-78ab4d6134fe"))
		/* dual interface */ BuildDependency;
	enum vsBuildState;
	struct __declspec(uuid("e6b96cac-b8c7-40ae-b705-5c81878c4a9e"))
		/* dual interface */ Commands;
	struct __declspec(uuid("5fe10fb0-91a1-4e55-baaa-eccae5cceb94"))
		/* dual interface */ Command;
	enum vsCommandBarType;
	struct __declspec(uuid("6caa67cf-43ae-4184-aaab-0200ddf6b240"))
		/* dual interface */ SelectedItems;
	struct __declspec(uuid("049d2cdf-3731-4cb6-a233-be97bce922d3"))
		/* dual interface */ SelectedItem;
	struct __declspec(uuid("02273422-8dd4-4a9f-8a8b-d70443d510f4"))
		/* dual interface */ SelectionContainer;
	struct __declspec(uuid("e577442a-98e1-46c5-bd2e-d25807ec81ce"))
		/* dual interface */ WindowConfigurations;
	struct __declspec(uuid("41d02413-8a67-4c28-a980-ad7539ed415d"))
		/* dual interface */ WindowConfiguration;
	struct __declspec(uuid("c34301a1-3ef1-41d8-932a-fea4a8a8ce0c"))
		/* dual interface */ StatusBar;
	struct __declspec(uuid("8d0aa9cc-8465-42f3-ad6e-dfde28ccc75d"))
		/* dual interface */ ObjectExtenders;
	struct __declspec(uuid("4db06329-23f4-443b-9abd-9cf611e8ae07"))
		/* dual interface */ IExtenderProvider;
	struct __declspec(uuid("e57c510b-968b-4a3c-a467-ee4013157dc9"))
		/* dual interface */ IExtenderSite;
	struct __declspec(uuid("f69b64a3-9017-4e48-9784-e152b51aa722"))
		/* dual interface */ IExtenderProviderUnk;
	struct __declspec(uuid("40d4b9b6-739b-4965-8d65-692aec692266"))
		/* dual interface */ Find;
	enum vsFindAction;
	enum vsFindPatternSyntax;
	enum vsFindTarget;
	enum vsFindResultsLocation;
	enum wizardResult;
	struct __declspec(uuid("d5dbe57b-c074-4e95-b015-abeeaa391693"))
		/* dual interface */ ItemOperations;
	enum vsNavigateOptions;
	enum vsPromptResult;
	struct __declspec(uuid("d8dec44d-caf2-4b39-a539-b91ae921ba92"))
		/* dual interface */ UndoContext;
	struct __declspec(uuid("f9f99155-6d4d-49b1-ad63-c78c3e8a5916"))
		/* dual interface */ Macros;
	struct __declspec(uuid("f1ddc2c2-df76-4ebb-9de8-48ad2557062c"))
		/* dual interface */ SourceControl;
	struct __declspec(uuid("f47dc7e7-84b6-474f-bb91-631640aa0560"))
		/* dual interface */ TextBuffer;
	struct __declspec(uuid("6659ed14-2ab6-47f3-a890-00c8aba43b84"))
		/* interface */ ISupportVSProperties;
	struct __declspec(uuid("56fcd5af-7f17-4c5c-aa8d-ae2bb2ddbf38"))
		/* dual interface */ ToolBox;
	struct __declspec(uuid("ce2def9e-3387-4bf2-967b-a1f7f70df325"))
		/* dual interface */ ToolBoxTab;
	struct __declspec(uuid("cf177b52-4f2f-42a0-8da3-ce78679a0d2d"))
		/* dual interface */ ToolBoxTabs;
	struct __declspec(uuid("395c7dfb-f158-431c-8f43-dda83b4ef54e"))
		/* dual interface */ ToolBoxItems;
	struct __declspec(uuid("46538d1b-4d81-4002-8bb4-dbdb65babb23"))
		/* dual interface */ ToolBoxItem;
	enum vsToolBoxItemFormat;
	struct __declspec(uuid("eb5be8a7-e593-4de6-a923-c2afecb96336"))
		/* interface */ IExtensibleObjectSite;
	struct __declspec(uuid("8cc0cde1-c16a-4749-99af-6f7523c34a57"))
		/* interface */ IVsProfferCommands;
	struct __declspec(uuid("aade1f59-6ace-43d1-8fca-42af3a5c4f3c"))
		/* dual interface */ IFilterProperties;
	enum vsFilterProperties;
	struct __declspec(uuid("fbd0d024-09cd-4d9f-9e2b-cacd628426a5"))
		/* dual interface */ UIHierarchyItem;
	struct __declspec(uuid("db8406b0-a916-449c-a277-bb04028f4394"))
		/* dual interface */ UIHierarchyItems;
	enum vsUISelectionType;
	struct __declspec(uuid("72a2a2ef-c209-408c-a377-76871774adb7"))
		/* dual interface */ UIHierarchy;
	struct __declspec(uuid("9ff3ddca-1795-4191-a5b1-02d1ae35d074"))
		/* dual interface */ TextEditor;
	enum vsCMPrototype;
	struct __declspec(uuid("d4bb39fb-0f0e-11d3-b880-00c04f79e479"))
		/* dual interface */ _DebuggerEventsRoot;
	enum vsNavigateBrowser;
	enum vsCommandDisabledFlags;
	enum vsInitializeMode;
	struct __declspec(uuid("e914bbe1-03a4-11d1-bbcd-00a0c90f2744"))
		/* dual interface */ IDTWizard;
	struct __declspec(uuid("3c536122-57b1-46de-ab34-acc524140093"))
		/* interface */ IVsExtensibility;
	struct __declspec(uuid("e2cc506a-588b-4f65-a1f0-2244c060abcb"))
		/* interface */ IVsGlobalsCallback;
	struct __declspec(uuid("192ac688-e7c6-4f9d-8180-4b37efbf6f3a"))
		/* interface */ IVsGlobals;
	enum vsCommandStatusTextWanted;
	enum vsCommandStatus;
	enum vsCommandExecOption;
	struct __declspec(uuid("7ef39a3e-590d-4879-88d4-c9be5bcfd92e"))
		/* dual interface */ IDTCommandTarget;
	struct __declspec(uuid("509b9955-7303-48c9-90d4-e165b974e6ba"))
		/* dual interface */ CommandWindow;
	struct __declspec(uuid("85451f83-b5ca-437f-a619-0cb705707420"))
		/* dual interface */ _ProjectsEvents;
	struct __declspec(uuid("7f508d55-627f-4d7f-be0b-9e3d829ff0ed"))
		/* dispinterface */ _dispProjectsEvents;
	struct /* coclass */ ProjectsEvents;
	struct __declspec(uuid("7658b944-f37b-11d2-aacf-00c04f688dde"))
		/* dual interface */ _MiscSlnFilesEventsRoot;
	struct __declspec(uuid("9e66fe98-a1c6-421d-8c0c-6da4e652e770"))
		/* interface */ _CommandBarControlEvents;
	struct __declspec(uuid("987fb893-f96d-11d0-bbbb-00a0c90f2744"))
		/* dispinterface */ _dispCommandBarControlEvents;
	struct /* coclass */ CommandBarEvents;
	enum vsBuildKind;
	struct __declspec(uuid("dc5437f4-f114-11d2-aacf-00c04f688dde"))
		/* dual interface */ _DocumentEventsRoot;
	enum vsTextChanged;
	struct __declspec(uuid("b3c38885-b288-44a8-b290-34fe63bf3c76"))
		/* dual interface */ _TextEditorEventsRoot;
	struct __declspec(uuid("17d12026-ba99-403e-a359-71fd1e5a72cd"))
		/* dual interface */ _WindowEventsRoot;
	struct __declspec(uuid("aa6f4085-33b6-4629-b9ea-692101007cc2"))
		/* dual interface */ _OutputWindowEventsRoot;
	struct __declspec(uuid("6bc8c372-c6f0-4be6-b255-827ac190bf71"))
		/* dual interface */ _TaskListEventsRoot;
	enum vsStatusAnimation;
	struct __declspec(uuid("bdcaf240-2692-4713-902a-b110b1d0f100"))
		/* dual interface */ IDTToolsOptionsPage;
	enum vsStartUp;
	enum vsFontCharSet;
	enum vsBrowserViewSource;
	struct __declspec(uuid("a3286b03-5ac6-44f0-8cc3-ebed7f1124e5"))
		/* dual interface */ _EnvironmentWebBrowser;
	struct __declspec(uuid("aebded64-a206-11d3-b8b5-00c04f79f802"))
		/* dual interface */ _EnvironmentHelp;
	struct __declspec(uuid("e5d17051-d6e5-4da7-8b3a-ca888617a5e7"))
		/* dual interface */ ColorableItems;
	struct __declspec(uuid("f25ae7e6-1460-4ba4-8e5e-bbbe746de353"))
		/* dual interface */ FontsAndColorsItems;
	struct __declspec(uuid("256068f6-1add-4f7b-ba76-571314c413ad"))
		/* dual interface */ _FontsAndColors;
	struct __declspec(uuid("48e61d9c-8c8d-42d3-914b-46d70c8b7a40"))
		/* dual interface */ _EnvironmentGeneral;
	struct __declspec(uuid("f809cab6-2c9f-41f2-a5af-e26fb80e62ad"))
		/* dual interface */ _EnvironmentFontsAndColors;
	struct __declspec(uuid("478f06d4-5d57-473f-9b74-5f8e88efa5e7"))
		/* dual interface */ _EnvironmentProjectsAndSolution;
	struct __declspec(uuid("76ed1c48-ed86-4e9e-acf8-a40e765daf25"))
		/* dual interface */ _EnvironmentDocuments;
	struct __declspec(uuid("4bc18a5b-dbb6-4af5-a443-2e3f19365304"))
		/* dual interface */ _EnvironmentTaskList;
	struct __declspec(uuid("9c722678-490d-408f-98ae-b6b9a68aa45d"))
		/* dual interface */ _EnvironmentKeyboard;
	struct __declspec(uuid("86c31347-5b52-4715-b454-a6e5fcab975d"))
		/* interface */ IExtensibleObject;
	enum vsFindOptions;
	enum vsMovementOptions;
	enum vsGoToLineOptions;
	enum vsSmartFormatOptions;
	enum vsInsertFlags;
	enum vsMoveToColumnLine;
	enum vsEPReplaceTextOptions;
	struct __declspec(uuid("2e1bfd1c-5b26-4aca-b97b-ed9d261ba3e7"))
		/* dual interface */ IVsTextEditGeneral;
	struct __declspec(uuid("5943bd7e-d722-42db-a251-fe2add8711ea"))
		/* dual interface */ IVsTextEditPerLanguage;
	enum _vsIndentStyle;
	struct __declspec(uuid("f39ab913-e6c9-4546-a265-1e43f8de924c"))
		/* dual interface */ IVsTextEditFonts;
	enum vsext_FontCharSet;
	enum VSEXECRESULT;
	enum vsext_DisplayMode;
	enum vsext_WindowType;
	enum vsext_WindowState;
	enum vsext_LinkedWindowType;
	enum vsext_StartUp;
	enum vsext_Build;
	enum DsTextSearchOptions;
	enum DsSaveChanges;
	enum DsGoToLineOptions;
	enum DsStartOfLineOptions;
	enum DsMovementOptions;
	enum DsWhitespaceOptions;
	enum DsCaseOptions;
	enum dsSaveStatus;
	enum vsHTMLTabs;
	struct __declspec(uuid("f6576203-fbce-477e-a66b-eda237bb68a7"))
		/* dual interface */ HTMLWindow;
#if !defined(_WIN64)
	typedef __w64 unsigned long ULONG_PTR;
#else
	typedef unsigned __int64 ULONG_PTR;
#endif
#if !defined(_WIN64)
	typedef __w64 long LONG_PTR;
#else
	typedef __int64 LONG_PTR;
#endif
	typedef enum _vsIndentStyle vsIndentStyle;
	typedef enum VSEXECRESULT vs_exec_Result;

	//
	// Smart pointer typedef declarations
	//

	_COM_SMARTPTR_TYPEDEF(Globals, __uuidof(Globals));
	_COM_SMARTPTR_TYPEDEF(_CommandEvents, __uuidof(_CommandEvents));
	_COM_SMARTPTR_TYPEDEF(_dispCommandEvents, __uuidof(_dispCommandEvents));
	_COM_SMARTPTR_TYPEDEF(_SelectionEvents, __uuidof(_SelectionEvents));
	_COM_SMARTPTR_TYPEDEF(_dispSelectionEvents, __uuidof(_dispSelectionEvents));
	_COM_SMARTPTR_TYPEDEF(_SolutionEvents, __uuidof(_SolutionEvents));
	_COM_SMARTPTR_TYPEDEF(_dispSolutionEvents, __uuidof(_dispSolutionEvents));
	_COM_SMARTPTR_TYPEDEF(_BuildEvents, __uuidof(_BuildEvents));
	_COM_SMARTPTR_TYPEDEF(_dispBuildEvents, __uuidof(_dispBuildEvents));
	_COM_SMARTPTR_TYPEDEF(_WindowEvents, __uuidof(_WindowEvents));
	_COM_SMARTPTR_TYPEDEF(_dispWindowEvents, __uuidof(_dispWindowEvents));
	_COM_SMARTPTR_TYPEDEF(_OutputWindowEvents, __uuidof(_OutputWindowEvents));
	_COM_SMARTPTR_TYPEDEF(_dispOutputWindowEvents, __uuidof(_dispOutputWindowEvents));
	_COM_SMARTPTR_TYPEDEF(_FindEvents, __uuidof(_FindEvents));
	_COM_SMARTPTR_TYPEDEF(_dispFindEvents, __uuidof(_dispFindEvents));
	_COM_SMARTPTR_TYPEDEF(_TaskListEvents, __uuidof(_TaskListEvents));
	_COM_SMARTPTR_TYPEDEF(_dispTaskListEvents, __uuidof(_dispTaskListEvents));
	_COM_SMARTPTR_TYPEDEF(_DTEEvents, __uuidof(_DTEEvents));
	_COM_SMARTPTR_TYPEDEF(_dispDTEEvents, __uuidof(_dispDTEEvents));
	_COM_SMARTPTR_TYPEDEF(_DocumentEvents, __uuidof(_DocumentEvents));
	_COM_SMARTPTR_TYPEDEF(_dispDocumentEvents, __uuidof(_dispDocumentEvents));
	_COM_SMARTPTR_TYPEDEF(_ProjectItemsEvents, __uuidof(_ProjectItemsEvents));
	_COM_SMARTPTR_TYPEDEF(_dispProjectItemsEvents, __uuidof(_dispProjectItemsEvents));
	_COM_SMARTPTR_TYPEDEF(_DebuggerEvents, __uuidof(_DebuggerEvents));
	_COM_SMARTPTR_TYPEDEF(_dispDebuggerEvents, __uuidof(_dispDebuggerEvents));
	_COM_SMARTPTR_TYPEDEF(_TextEditorEvents, __uuidof(_TextEditorEvents));
	_COM_SMARTPTR_TYPEDEF(_dispTextEditorEvents, __uuidof(_dispTextEditorEvents));
	_COM_SMARTPTR_TYPEDEF(StatusBar, __uuidof(StatusBar));
	_COM_SMARTPTR_TYPEDEF(IExtenderSite, __uuidof(IExtenderSite));
	_COM_SMARTPTR_TYPEDEF(IExtenderProvider, __uuidof(IExtenderProvider));
	_COM_SMARTPTR_TYPEDEF(IExtenderProviderUnk, __uuidof(IExtenderProviderUnk));
	_COM_SMARTPTR_TYPEDEF(ObjectExtenders, __uuidof(ObjectExtenders));
	_COM_SMARTPTR_TYPEDEF(Find, __uuidof(Find));
	_COM_SMARTPTR_TYPEDEF(UndoContext, __uuidof(UndoContext));
	_COM_SMARTPTR_TYPEDEF(Macros, __uuidof(Macros));
	_COM_SMARTPTR_TYPEDEF(SourceControl, __uuidof(SourceControl));
	_COM_SMARTPTR_TYPEDEF(ISupportVSProperties, __uuidof(ISupportVSProperties));
	_COM_SMARTPTR_TYPEDEF(IExtensibleObjectSite, __uuidof(IExtensibleObjectSite));
	_COM_SMARTPTR_TYPEDEF(IVsProfferCommands, __uuidof(IVsProfferCommands));
	_COM_SMARTPTR_TYPEDEF(IFilterProperties, __uuidof(IFilterProperties));
	_COM_SMARTPTR_TYPEDEF(TextEditor, __uuidof(TextEditor));
	_COM_SMARTPTR_TYPEDEF(_DebuggerEventsRoot, __uuidof(_DebuggerEventsRoot));
	_COM_SMARTPTR_TYPEDEF(IDTWizard, __uuidof(IDTWizard));
	_COM_SMARTPTR_TYPEDEF(IVsGlobalsCallback, __uuidof(IVsGlobalsCallback));
	_COM_SMARTPTR_TYPEDEF(IVsGlobals, __uuidof(IVsGlobals));
	_COM_SMARTPTR_TYPEDEF(IDTCommandTarget, __uuidof(IDTCommandTarget));
	_COM_SMARTPTR_TYPEDEF(_ProjectsEvents, __uuidof(_ProjectsEvents));
	_COM_SMARTPTR_TYPEDEF(_dispProjectsEvents, __uuidof(_dispProjectsEvents));
	_COM_SMARTPTR_TYPEDEF(_MiscSlnFilesEventsRoot, __uuidof(_MiscSlnFilesEventsRoot));
	_COM_SMARTPTR_TYPEDEF(_CommandBarControlEvents, __uuidof(_CommandBarControlEvents));
	_COM_SMARTPTR_TYPEDEF(_dispCommandBarControlEvents, __uuidof(_dispCommandBarControlEvents));
	_COM_SMARTPTR_TYPEDEF(_OutputWindowEventsRoot, __uuidof(_OutputWindowEventsRoot));
	_COM_SMARTPTR_TYPEDEF(_TaskListEventsRoot, __uuidof(_TaskListEventsRoot));
	_COM_SMARTPTR_TYPEDEF(IDTToolsOptionsPage, __uuidof(IDTToolsOptionsPage));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentWebBrowser, __uuidof(_EnvironmentWebBrowser));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentHelp, __uuidof(_EnvironmentHelp));
	_COM_SMARTPTR_TYPEDEF(ColorableItems, __uuidof(ColorableItems));
	_COM_SMARTPTR_TYPEDEF(FontsAndColorsItems, __uuidof(FontsAndColorsItems));
	_COM_SMARTPTR_TYPEDEF(_FontsAndColors, __uuidof(_FontsAndColors));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentGeneral, __uuidof(_EnvironmentGeneral));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentFontsAndColors, __uuidof(_EnvironmentFontsAndColors));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentProjectsAndSolution, __uuidof(_EnvironmentProjectsAndSolution));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentDocuments, __uuidof(_EnvironmentDocuments));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentTaskList, __uuidof(_EnvironmentTaskList));
	_COM_SMARTPTR_TYPEDEF(_EnvironmentKeyboard, __uuidof(_EnvironmentKeyboard));
	_COM_SMARTPTR_TYPEDEF(IExtensibleObject, __uuidof(IExtensibleObject));
	_COM_SMARTPTR_TYPEDEF(IVsTextEditGeneral, __uuidof(IVsTextEditGeneral));
	_COM_SMARTPTR_TYPEDEF(IVsTextEditPerLanguage, __uuidof(IVsTextEditPerLanguage));
	_COM_SMARTPTR_TYPEDEF(IVsTextEditFonts, __uuidof(IVsTextEditFonts));
	_COM_SMARTPTR_TYPEDEF(_DTE, __uuidof(_DTE));
	_COM_SMARTPTR_TYPEDEF(Windows, __uuidof(Windows));
	_COM_SMARTPTR_TYPEDEF(Window, __uuidof(Window));
	_COM_SMARTPTR_TYPEDEF(LinkedWindows, __uuidof(LinkedWindows));
	_COM_SMARTPTR_TYPEDEF(TextBuffer, __uuidof(TextBuffer));
	_COM_SMARTPTR_TYPEDEF(_WindowEventsRoot, __uuidof(_WindowEventsRoot));
	_COM_SMARTPTR_TYPEDEF(HTMLWindow, __uuidof(HTMLWindow));
	_COM_SMARTPTR_TYPEDEF(ProjectItem, __uuidof(ProjectItem));
	_COM_SMARTPTR_TYPEDEF(ItemOperations, __uuidof(ItemOperations));
	_COM_SMARTPTR_TYPEDEF(ProjectItems, __uuidof(ProjectItems));
	_COM_SMARTPTR_TYPEDEF(Project, __uuidof(Project));
	_COM_SMARTPTR_TYPEDEF(Projects, __uuidof(Projects));
	_COM_SMARTPTR_TYPEDEF(Properties, __uuidof(Properties));
	_COM_SMARTPTR_TYPEDEF(Property, __uuidof(Property));
	_COM_SMARTPTR_TYPEDEF(ConfigurationManager, __uuidof(ConfigurationManager));
	_COM_SMARTPTR_TYPEDEF(Configuration, __uuidof(Configuration));
	_COM_SMARTPTR_TYPEDEF(Configurations, __uuidof(Configurations));
	_COM_SMARTPTR_TYPEDEF(OutputGroups, __uuidof(OutputGroups));
	_COM_SMARTPTR_TYPEDEF(OutputGroup, __uuidof(OutputGroup));
	_COM_SMARTPTR_TYPEDEF(CodeModel, __uuidof(CodeModel));
	_COM_SMARTPTR_TYPEDEF(CodeElements, __uuidof(CodeElements));
	_COM_SMARTPTR_TYPEDEF(CodeElement, __uuidof(CodeElement));
	_COM_SMARTPTR_TYPEDEF(TextPoint, __uuidof(TextPoint));
	_COM_SMARTPTR_TYPEDEF(VirtualPoint, __uuidof(VirtualPoint));
	_COM_SMARTPTR_TYPEDEF(CodeAttribute, __uuidof(CodeAttribute));
	_COM_SMARTPTR_TYPEDEF(TextDocument, __uuidof(TextDocument));
	_COM_SMARTPTR_TYPEDEF(CommandWindow, __uuidof(CommandWindow));
	_COM_SMARTPTR_TYPEDEF(_TextEditorEventsRoot, __uuidof(_TextEditorEventsRoot));
	_COM_SMARTPTR_TYPEDEF(Document, __uuidof(Document));
	_COM_SMARTPTR_TYPEDEF(Documents, __uuidof(Documents));
	_COM_SMARTPTR_TYPEDEF(Events, __uuidof(Events));
	_COM_SMARTPTR_TYPEDEF(IVsExtensibility, __uuidof(IVsExtensibility));
	_COM_SMARTPTR_TYPEDEF(_DocumentEventsRoot, __uuidof(_DocumentEventsRoot));
	_COM_SMARTPTR_TYPEDEF(TextSelection, __uuidof(TextSelection));
	_COM_SMARTPTR_TYPEDEF(TextRanges, __uuidof(TextRanges));
	_COM_SMARTPTR_TYPEDEF(EditPoint, __uuidof(EditPoint));
	_COM_SMARTPTR_TYPEDEF(TextRange, __uuidof(TextRange));
	_COM_SMARTPTR_TYPEDEF(TextPane, __uuidof(TextPane));
	_COM_SMARTPTR_TYPEDEF(TextPanes, __uuidof(TextPanes));
	_COM_SMARTPTR_TYPEDEF(TextWindow, __uuidof(TextWindow));
	_COM_SMARTPTR_TYPEDEF(CodeType, __uuidof(CodeType));
	_COM_SMARTPTR_TYPEDEF(CodeTypeRef, __uuidof(CodeTypeRef));
	_COM_SMARTPTR_TYPEDEF(CodeParameter, __uuidof(CodeParameter));
	_COM_SMARTPTR_TYPEDEF(CodeFunction, __uuidof(CodeFunction));
	_COM_SMARTPTR_TYPEDEF(CodeVariable, __uuidof(CodeVariable));
	_COM_SMARTPTR_TYPEDEF(CodeNamespace, __uuidof(CodeNamespace));
	_COM_SMARTPTR_TYPEDEF(CodeEnum, __uuidof(CodeEnum));
	_COM_SMARTPTR_TYPEDEF(CodeClass, __uuidof(CodeClass));
	_COM_SMARTPTR_TYPEDEF(CodeProperty, __uuidof(CodeProperty));
	_COM_SMARTPTR_TYPEDEF(CodeInterface, __uuidof(CodeInterface));
	_COM_SMARTPTR_TYPEDEF(CodeDelegate, __uuidof(CodeDelegate));
	_COM_SMARTPTR_TYPEDEF(CodeStruct, __uuidof(CodeStruct));
	_COM_SMARTPTR_TYPEDEF(FileCodeModel, __uuidof(FileCodeModel));
	_COM_SMARTPTR_TYPEDEF(ContextAttributes, __uuidof(ContextAttributes));
	_COM_SMARTPTR_TYPEDEF(ContextAttribute, __uuidof(ContextAttribute));
	_COM_SMARTPTR_TYPEDEF(AddIn, __uuidof(AddIn));
	_COM_SMARTPTR_TYPEDEF(AddIns, __uuidof(AddIns));
	_COM_SMARTPTR_TYPEDEF(OutputWindowPane, __uuidof(OutputWindowPane));
	_COM_SMARTPTR_TYPEDEF(OutputWindowPanes, __uuidof(OutputWindowPanes));
	_COM_SMARTPTR_TYPEDEF(OutputWindow, __uuidof(OutputWindow));
	_COM_SMARTPTR_TYPEDEF(TaskItem, __uuidof(TaskItem));
	_COM_SMARTPTR_TYPEDEF(TaskItems, __uuidof(TaskItems));
	_COM_SMARTPTR_TYPEDEF(TaskList, __uuidof(TaskList));
	_COM_SMARTPTR_TYPEDEF(Process, __uuidof(Process));
	_COM_SMARTPTR_TYPEDEF(Programs, __uuidof(Programs));
	_COM_SMARTPTR_TYPEDEF(Program, __uuidof(Program));
	_COM_SMARTPTR_TYPEDEF(Threads, __uuidof(Threads));
	_COM_SMARTPTR_TYPEDEF(Thread, __uuidof(Thread));
	_COM_SMARTPTR_TYPEDEF(StackFrames, __uuidof(StackFrames));
	_COM_SMARTPTR_TYPEDEF(StackFrame, __uuidof(StackFrame));
	_COM_SMARTPTR_TYPEDEF(Expressions, __uuidof(Expressions));
	_COM_SMARTPTR_TYPEDEF(Expression, __uuidof(Expression));
	_COM_SMARTPTR_TYPEDEF(Debugger, __uuidof(Debugger));
	_COM_SMARTPTR_TYPEDEF(Processes, __uuidof(Processes));
	_COM_SMARTPTR_TYPEDEF(Breakpoints, __uuidof(Breakpoints));
	_COM_SMARTPTR_TYPEDEF(Breakpoint, __uuidof(Breakpoint));
	_COM_SMARTPTR_TYPEDEF(Languages, __uuidof(Languages));
	_COM_SMARTPTR_TYPEDEF(Language, __uuidof(Language));
	_COM_SMARTPTR_TYPEDEF(_Solution, __uuidof(_Solution));
	_COM_SMARTPTR_TYPEDEF(SolutionBuild, __uuidof(SolutionBuild));
	_COM_SMARTPTR_TYPEDEF(SolutionConfiguration, __uuidof(SolutionConfiguration));
	_COM_SMARTPTR_TYPEDEF(SolutionConfigurations, __uuidof(SolutionConfigurations));
	_COM_SMARTPTR_TYPEDEF(SolutionContexts, __uuidof(SolutionContexts));
	_COM_SMARTPTR_TYPEDEF(SolutionContext, __uuidof(SolutionContext));
	_COM_SMARTPTR_TYPEDEF(BuildDependencies, __uuidof(BuildDependencies));
	_COM_SMARTPTR_TYPEDEF(BuildDependency, __uuidof(BuildDependency));
	_COM_SMARTPTR_TYPEDEF(Commands, __uuidof(Commands));
	_COM_SMARTPTR_TYPEDEF(Command, __uuidof(Command));
	_COM_SMARTPTR_TYPEDEF(SelectedItems, __uuidof(SelectedItems));
	_COM_SMARTPTR_TYPEDEF(SelectedItem, __uuidof(SelectedItem));
	_COM_SMARTPTR_TYPEDEF(SelectionContainer, __uuidof(SelectionContainer));
	_COM_SMARTPTR_TYPEDEF(WindowConfigurations, __uuidof(WindowConfigurations));
	_COM_SMARTPTR_TYPEDEF(WindowConfiguration, __uuidof(WindowConfiguration));
	_COM_SMARTPTR_TYPEDEF(ToolBox, __uuidof(ToolBox));
	_COM_SMARTPTR_TYPEDEF(ToolBoxTab, __uuidof(ToolBoxTab));
	_COM_SMARTPTR_TYPEDEF(ToolBoxTabs, __uuidof(ToolBoxTabs));
	_COM_SMARTPTR_TYPEDEF(ToolBoxItems, __uuidof(ToolBoxItems));
	_COM_SMARTPTR_TYPEDEF(ToolBoxItem, __uuidof(ToolBoxItem));
	_COM_SMARTPTR_TYPEDEF(UIHierarchyItem, __uuidof(UIHierarchyItem));
	_COM_SMARTPTR_TYPEDEF(UIHierarchyItems, __uuidof(UIHierarchyItems));
	_COM_SMARTPTR_TYPEDEF(UIHierarchy, __uuidof(UIHierarchy));

	//
	// Type library items
	//

	enum __declspec(uuid("cd678662-7939-48a0-bf1d-fa1bed56c130"))
		vsWindowState
	{
		vsWindowStateNormal = 0,
		vsWindowStateMinimize = 1,
		vsWindowStateMaximize = 2
	};

	enum __declspec(uuid("705cd8a9-d29b-11d2-aabd-00c04f688dde"))
		vsWindowType
	{
		vsWindowTypeCodeWindow = 0,
		vsWindowTypeDesigner = 1,
		vsWindowTypeBrowser = 2,
		vsWindowTypeWatch = 3,
		vsWindowTypeLocals = 4,
		vsWindowTypeImmediate = 5,
		vsWindowTypeSolutionExplorer = 6,
		vsWindowTypeProperties = 7,
		vsWindowTypeFind = 8,
		vsWindowTypeFindReplace = 9,
		vsWindowTypeToolbox = 10,
		vsWindowTypeLinkedWindowFrame = 11,
		vsWindowTypeMainWindow = 12,
		vsWindowTypePreview = 13,
		vsWindowTypeColorPalette = 14,
		vsWindowTypeToolWindow = 15,
		vsWindowTypeDocument = 16,
		vsWindowTypeOutput = 17,
		vsWindowTypeTaskList = 18,
		vsWindowTypeAutos = 19,
		vsWindowTypeCallStack = 20,
		vsWindowTypeThreads = 21,
		vsWindowTypeDocumentOutline = 22,
		vsWindowTypeRunningDocuments = 23
	};

	struct __declspec(uuid("3c9cfe1e-389f-4118-9fad-************"))
		DTE;
	// [ default ] interface _DTE

	enum __declspec(uuid("612256bd-7ba2-45c4-b60f-7b2ae42f96c3"))
		vsConfigurationType
	{
		vsConfigurationTypeProject = 1,
		vsConfigurationTypeProjectItem = 2
	};

	struct __declspec(uuid("e68a3e0e-b435-4dde-86b7-f5adefc19df2"))
		Globals : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * pDTE) = 0;
		virtual HRESULT __stdcall get_VariableValue(
			BSTR VariableName,
			/*[out,retval]*/ VARIANT * pVal) = 0;
		virtual HRESULT __stdcall put_VariableValue(
			BSTR VariableName,
			/*[in]*/ VARIANT pVal) = 0;
		virtual HRESULT __stdcall put_VariablePersists(
			BSTR VariableName,
			/*[in]*/ VARIANT_BOOL pVal) = 0;
		virtual HRESULT __stdcall get_VariablePersists(
			BSTR VariableName,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_VariableExists(
			BSTR Name,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_VariableNames(
			/*[out,retval]*/ VARIANT * Names) = 0;
	};

	enum vsCMElement
	{
		vsCMElementOther = 0,
		vsCMElementClass = 1,
		vsCMElementFunction = 2,
		vsCMElementVariable = 3,
		vsCMElementProperty = 4,
		vsCMElementNamespace = 5,
		vsCMElementParameter = 6,
		vsCMElementAttribute = 7,
		vsCMElementInterface = 8,
		vsCMElementDelegate = 9,
		vsCMElementEnum = 10,
		vsCMElementStruct = 11,
		vsCMElementUnion = 12,
		vsCMElementLocalDeclStmt = 13,
		vsCMElementFunctionInvokeStmt = 14,
		vsCMElementPropertySetStmt = 15,
		vsCMElementAssignmentStmt = 16,
		vsCMElementInheritsStmt = 17,
		vsCMElementImplementsStmt = 18,
		vsCMElementOptionStmt = 19,
		vsCMElementVBAttributeStmt = 20,
		vsCMElementVBAttributeGroup = 21,
		vsCMElementEventsDeclaration = 22,
		vsCMElementUDTDecl = 23,
		vsCMElementDeclareDecl = 24,
		vsCMElementDefineStmt = 25,
		vsCMElementTypeDef = 26,
		vsCMElementIncludeStmt = 27,
		vsCMElementUsingStmt = 28,
		vsCMElementMacro = 29,
		vsCMElementMap = 30,
		vsCMElementIDLImport = 31,
		vsCMElementIDLImportLib = 32,
		vsCMElementIDLCoClass = 33,
		vsCMElementIDLLibrary = 34,
		vsCMElementImportStmt = 35,
		vsCMElementMapEntry = 36,
		vsCMElementVCBase = 37,
		vsCMElementEvent = 38,
		vsCMElementModule = 39
	};

	enum vsCMInfoLocation
	{
		vsCMInfoLocationProject = 1,
		vsCMInfoLocationExternal = 2,
		vsCMInfoLocationNone = 4,
		vsCMInfoLocationVirtual = 8
	};

	enum __declspec(uuid("aae3fae8-8afc-11d2-adee-00c04f7971c3"))
		vsSaveChanges
	{
		vsSaveChangesYes = 1,
		vsSaveChangesNo = 2,
		vsSaveChangesPrompt = 3
	};

	enum __declspec(uuid("8901ff7a-e6e8-4154-a505-135ee434817e"))
		vsSaveStatus
	{
		vsSaveCancelled = 2,
		vsSaveSucceeded = 1
	};

	enum __declspec(uuid("98a2f403-007b-41f8-9a18-87addef1760f"))
		vsCaseOptions
	{
		vsCaseOptionsLowercase = 1,
		vsCaseOptionsUppercase = 2,
		vsCaseOptionsCapitalize = 3
	};

	enum __declspec(uuid("bbdfcfe4-4c68-4736-b991-f85fa955d585"))
		vsWhitespaceOptions
	{
		vsWhitespaceOptionsHorizontal = 0,
		vsWhitespaceOptionsVertical = 1
	};

	enum __declspec(uuid("f05ea427-01ba-46dc-8392-fb4aa7e5eb3a"))
		vsStartOfLineOptions
	{
		vsStartOfLineOptionsFirstColumn = 0,
		vsStartOfLineOptionsFirstText = 1
	};

	enum __declspec(uuid("b26ac3c2-3981-4a2e-9d6f-559b41cd1cd2"))
		vsPaneShowHow
	{
		vsPaneShowCentered = 0,
		vsPaneShowTop = 1,
		vsPaneShowAsIs = 2
	};

	enum __declspec(uuid("f3128012-b41f-46ec-8794-f716db9697d0"))
		vsSelectionMode
	{
		vsSelectionModeStream = 0,
		vsSelectionModeBox = 1
	};

	enum vsCMPart
	{
		vsCMPartName = 1,
		vsCMPartAttributes = 2,
		vsCMPartHeader = 4,
		vsCMPartWhole = 8,
		vsCMPartBody = 16,
		vsCMPartNavigate = 32,
		vsCMPartAttributesWithDelimiter = 68,
		vsCMPartBodyWithDelimiter = 80,
		vsCMPartHeaderWithAttributes = 6,
		vsCMPartWholeWithAttributes = 10
	};

	enum vsCMAccess
	{
		vsCMAccessPublic = 1,
		vsCMAccessPrivate = 2,
		vsCMAccessProject = 4,
		vsCMAccessProtected = 8,
		vsCMAccessDefault = 32,
		vsCMAccessAssemblyOrFamily = 64,
		vsCMAccessWithEvents = 128,
		vsCMAccessProjectOrProtected = 12
	};

	enum vsCMFunction
	{
		vsCMFunctionOther = 0,
		vsCMFunctionConstructor = 1,
		vsCMFunctionPropertyGet = 2,
		vsCMFunctionPropertyLet = 4,
		vsCMFunctionPropertySet = 8,
		vsCMFunctionPutRef = 16,
		vsCMFunctionPropertyAssign = 32,
		vsCMFunctionSub = 64,
		vsCMFunctionFunction = 128,
		vsCMFunctionTopLevel = 256,
		vsCMFunctionDestructor = 512,
		vsCMFunctionOperator = 1024,
		vsCMFunctionVirtual = 2048,
		vsCMFunctionPure = 4096,
		vsCMFunctionConstant = 8192,
		vsCMFunctionShared = 16384,
		vsCMFunctionInline = 32768,
		vsCMFunctionComMethod = 65536
	};

	enum vsCMTypeRef
	{
		vsCMTypeRefOther = 0,
		vsCMTypeRefCodeType = 1,
		vsCMTypeRefArray = 2,
		vsCMTypeRefVoid = 3,
		vsCMTypeRefPointer = 4,
		vsCMTypeRefString = 5,
		vsCMTypeRefObject = 6,
		vsCMTypeRefByte = 7,
		vsCMTypeRefChar = 8,
		vsCMTypeRefShort = 9,
		vsCMTypeRefInt = 10,
		vsCMTypeRefLong = 11,
		vsCMTypeRefFloat = 12,
		vsCMTypeRefDouble = 13,
		vsCMTypeRefDecimal = 14,
		vsCMTypeRefBool = 15,
		vsCMTypeRefVariant = 16
	};

	enum __declspec(uuid("410202e1-b83a-41dd-aab0-8c0029fe9435"))
		vsContextAttributeType
	{
		vsContextAttributeFilter = 1,
		vsContextAttributeLookup = 2,
		vsContextAttributeLookupF1 = 3
	};

	enum __declspec(uuid("59286edc-d580-4e22-96c9-44d15b6148cc"))
		vsContextAttributes
	{
		vsContextAttributesGlobal = 1,
		vsContextAttributesWindow = 2,
		vsContextAttributesHighPriority = 3
	};

	enum __declspec(uuid("539f9c17-db5f-421d-b02b-07bcf9faa92f"))
		vsLinkedWindowType
	{
		vsLinkedWindowTypeDocked = 0,
		vsLinkedWindowTypeVertical = 2,
		vsLinkedWindowTypeHorizontal = 3,
		vsLinkedWindowTypeTabbed = 1
	};

	struct __declspec(uuid("1ded92b5-9a46-4b29-93ef-b5e07016659e"))
		CommandEvents;
	// [ default ] interface _CommandEvents
	// [ default, source ] dispinterface _dispCommandEvents

	struct __declspec(uuid("a79fc678-0d0a-496a-b9dc-0d5b9e1ca9fc"))
		_CommandEvents : IDispatch
	{};

	struct __declspec(uuid("ff2d5c12-feea-11d0-bbc8-00a0c90f2744"))
		_dispCommandEvents : IDispatch
	{};

	struct __declspec(uuid("af37511e-9e9d-4234-a5a1-7584d290e061"))
		SelectionEvents;
	// [ default ] interface _SelectionEvents
	// [ default, source ] dispinterface _dispSelectionEvents

	struct __declspec(uuid("eb6783db-1819-496d-84a4-3cff883010f6"))
		_SelectionEvents : IDispatch
	{};

	struct __declspec(uuid("deeb28b3-23e6-11d1-ae5c-00a0c90f26f4"))
		_dispSelectionEvents : IDispatch
	{};

	struct __declspec(uuid("88ac98c7-b38c-404b-bd86-d2a4f2e89dca"))
		SolutionEvents;
	// [ default ] interface _SolutionEvents
	// [ default, source ] dispinterface _dispSolutionEvents

	struct __declspec(uuid("bf8bbf37-5415-46a9-940d-594cad9dec26"))
		_SolutionEvents : IDispatch
	{};

	struct __declspec(uuid("fbcff1c2-261c-11d1-ae5e-00a0c90f26f4"))
		_dispSolutionEvents : IDispatch
	{};

	struct __declspec(uuid("d83d60e3-229f-4660-8dd0-28b629eedcda"))
		BuildEvents;
	// [ default ] interface _BuildEvents
	// [ default, source ] dispinterface _dispBuildEvents

	struct __declspec(uuid("794a2ba5-ffa6-4fc5-bf13-957b2c22edd7"))
		_BuildEvents : IDispatch
	{};

	struct __declspec(uuid("1926364e-6b90-46cb-a44d-8a80fb11acd9"))
		_dispBuildEvents : IDispatch
	{};

	enum __declspec(uuid("fa7fa701-efad-4895-a95a-d4ecb383bf7d"))
		vsBuildScope
	{
		vsBuildScopeSolution = 1,
		vsBuildScopeBatch = 2,
		vsBuildScopeProject = 3
	};

	enum __declspec(uuid("fa7fa702-efad-4895-a95a-d4ecb383bf7d"))
		vsBuildAction
	{
		vsBuildActionBuild = 1,
		vsBuildActionRebuildAll = 2,
		vsBuildActionClean = 3,
		vsBuildActionDeploy = 4
	};

	struct __declspec(uuid("2e260fd4-c130-4e6d-8ebc-4a3bfd188181"))
		WindowEvents;
	// [ default ] interface _WindowEvents
	// [ default, source ] dispinterface _dispWindowEvents

	struct __declspec(uuid("0d3a23a8-67bb-11d2-97c1-00c04fb6c6ff"))
		_WindowEvents : IDispatch
	{};

	struct __declspec(uuid("0d3a23a9-67bb-11d2-97c1-00c04fb6c6ff"))
		_dispWindowEvents : IDispatch
	{};

	struct __declspec(uuid("3760037f-b012-44f8-9c23-3609d7a16def"))
		OutputWindowEvents;
	// [ default ] interface _OutputWindowEvents
	// [ default, source ] dispinterface _dispOutputWindowEvents

	struct __declspec(uuid("0a3546a8-6840-11d2-97c1-00c04fb6c6ff"))
		_OutputWindowEvents : IDispatch
	{};

	struct __declspec(uuid("0d3a23af-67bb-11d2-97c1-00c04fb6c6ff"))
		_dispOutputWindowEvents : IDispatch
	{};

	enum __declspec(uuid("36b56de8-5a17-11d1-b2b6-00c04fb6c6ff"))
		vsTaskPriority
	{
		vsTaskPriorityLow = 1,
		vsTaskPriorityMedium = 2,
		vsTaskPriorityHigh = 3
	};

	enum __declspec(uuid("195dab2e-4854-11d2-8820-00c04fb6c6ff"))
		vsTaskIcon
	{
		vsTaskIconNone = 0,
		vsTaskIconCompile = 1,
		vsTaskIconSquiggle = 2,
		vsTaskIconComment = 3,
		vsTaskIconShortcut = 4,
		vsTaskIconUser = 5
	};

	struct __declspec(uuid("811322bc-042d-4828-bff2-640ef8b7209f"))
		FindEvents;
	// [ default ] interface _FindEvents
	// [ default, source ] dispinterface _dispFindEvents

	struct __declspec(uuid("c5331acd-c5d5-11d2-8598-006097c68e81"))
		_FindEvents : IDispatch
	{};

	struct __declspec(uuid("c5331ace-c5d5-11d2-8598-006097c68e81"))
		_dispFindEvents : IDispatch
	{};

	enum __declspec(uuid("6fa137be-32a8-4f54-8099-60bb955097a3"))
		vsFindResult
	{
		vsFindResultNotFound = 0,
		vsFindResultFound = 1,
		vsFindResultReplaceAndNotFound = 2,
		vsFindResultReplaceAndFound = 3,
		vsFindResultReplaced = 4,
		vsFindResultPending = 5,
		vsFindResultError = 6
	};

	struct __declspec(uuid("29617acd-7859-4328-be09-298f91f48196"))
		TaskListEvents;
	// [ default ] interface _TaskListEvents
	// [ default, source ] dispinterface _dispTaskListEvents

	struct __declspec(uuid("1125c422-49bd-11d2-8823-00c04fb6c6ff"))
		_TaskListEvents : IDispatch
	{};

	struct __declspec(uuid("1125c423-49bd-11d2-8823-00c04fb6c6ff"))
		_dispTaskListEvents : IDispatch
	{};

	enum __declspec(uuid("9848c17e-6aa5-11d2-97c3-00c04fb6c6ff"))
		vsTaskListColumn
	{
		vsTaskListColumnPriority = 1,
		vsTaskListColumnGlyph = 2,
		vsTaskListColumnCheck = 4,
		vsTaskListColumnDescription = 8,
		vsTaskListColumnFile = 16,
		vsTaskListColumnLine = 32
	};

	struct __declspec(uuid("c6304bab-6765-4c63-9017-4940aeb6f207"))
		DTEEvents;
	// [ default ] interface _DTEEvents
	// [ default, source ] dispinterface _dispDTEEvents

	struct __declspec(uuid("fa1bb6d7-ca83-11d2-aab2-00c04f688dde"))
		_DTEEvents : IDispatch
	{};

	struct __declspec(uuid("b50c9708-c909-4b87-a03d-af6cc4bfb422"))
		_dispDTEEvents : IDispatch
	{};

	enum __declspec(uuid("b638b3e3-c98b-4f59-9d05-105a620529f9"))
		vsIDEMode
	{
		vsIDEModeDesign = 1,
		vsIDEModeDebug = 2
	};

	struct __declspec(uuid("dc5437f7-f114-11d2-aacf-00c04f688dde"))
		DocumentEvents;
	// [ default ] interface _DocumentEvents
	// [ default, source ] dispinterface _dispDocumentEvents

	struct __declspec(uuid("dc5437f5-f114-11d2-aacf-00c04f688dde"))
		_DocumentEvents : IDispatch
	{};

	struct __declspec(uuid("dc5437f6-f114-11d2-aacf-00c04f688dde"))
		_dispDocumentEvents : IDispatch
	{};

	struct __declspec(uuid("de6c1098-93ca-4f49-bef0-262a13ca1176"))
		ProjectItemsEvents;
	// [ default ] interface _ProjectItemsEvents
	// [ default, source ] dispinterface _dispProjectItemsEvents

	struct __declspec(uuid("*************-410d-bf87-a7808eac977d"))
		_ProjectItemsEvents : IDispatch
	{};

	struct __declspec(uuid("6962753f-efd5-41c5-b083-d70687166aeb"))
		_dispProjectItemsEvents : IDispatch
	{};

	struct __declspec(uuid("0c763210-0fbb-11d3-b880-00c04f79e479"))
		DebuggerEvents;
	// [ default ] interface _DebuggerEvents
	// [ default, source ] dispinterface _dispDebuggerEvents

	struct __declspec(uuid("d4eae958-0fba-11d3-b880-00c04f79e479"))
		_DebuggerEvents : IDispatch
	{};

	struct __declspec(uuid("46209330-0fba-11d3-b880-00c04f79e479"))
		_dispDebuggerEvents : IDispatch
	{};

	enum __declspec(uuid("4106c580-c146-11d2-8ad1-00c04f79e479"))
		dbgEventReason
	{
		dbgEventReasonNone = 1,
		dbgEventReasonGo = 2,
		dbgEventReasonAttachProgram = 3,
		dbgEventReasonDetachProgram = 4,
		dbgEventReasonLaunchProgram = 5,
		dbgEventReasonEndProgram = 6,
		dbgEventReasonStopDebugging = 7,
		dbgEventReasonStep = 8,
		dbgEventReasonBreakpoint = 9,
		dbgEventReasonExceptionThrown = 10,
		dbgEventReasonExceptionNotHandled = 11,
		dbgEventReasonUserBreak = 12,
		dbgEventReasonContextSwitch = 13
	};

	enum __declspec(uuid("77940e85-bce0-443d-8b77-f5bd841a211e"))
		dbgExecutionAction
	{
		dbgExecutionActionDefault = 1,
		dbgExecutionActionGo = 2,
		dbgExecutionActionStopDebugging = 3,
		dbgExecutionActionStepInto = 4,
		dbgExecutionActionStepOut = 5,
		dbgExecutionActionStepOver = 6,
		dbgExecutionActionRunToCursor = 7
	};

	enum __declspec(uuid("ed2fbdd2-1eb1-11d3-b888-00c04f79e479"))
		dbgExceptionAction
	{
		dbgExceptionActionDefault = 1,
		dbgExceptionActionIgnore = 2,
		dbgExceptionActionBreak = 3,
		dbgExceptionActionContinue = 4
	};

	enum __declspec(uuid("75607dde-2b43-11d3-b88b-00c04f79e479"))
		dbgBreakpointType
	{
		dbgBreakpointTypePending = 1,
		dbgBreakpointTypeBound = 2
	};

	enum __declspec(uuid("229c054c-c146-11d2-8ad1-00c04f79e479"))
		dbgBreakpointLocationType
	{
		dbgBreakpointLocationTypeNone = 1,
		dbgBreakpointLocationTypeFunction = 2,
		dbgBreakpointLocationTypeFile = 3,
		dbgBreakpointLocationTypeData = 4,
		dbgBreakpointLocationTypeAddress = 5
	};

	enum __declspec(uuid("2a594772-c146-11d2-8ad1-00c04f79e479"))
		dbgBreakpointConditionType
	{
		dbgBreakpointConditionTypeWhenTrue = 1,
		dbgBreakpointConditionTypeWhenChanged = 2
	};

	enum __declspec(uuid("3a3329ce-c146-11d2-8ad1-00c04f79e479"))
		dbgHitCountType
	{
		dbgHitCountTypeNone = 1,
		dbgHitCountTypeEqual = 2,
		dbgHitCountTypeGreaterOrEqual = 3,
		dbgHitCountTypeMultiple = 4
	};

	enum __declspec(uuid("e940c60a-c68f-11d2-8ad6-00c04f79e479"))
		dbgDebugMode
	{
		dbgDesignMode = 1,
		dbgBreakMode = 2,
		dbgRunMode = 3
	};

	struct __declspec(uuid("adf22c37-0069-4adf-b12d-d8d47c38fe79"))
		TextEditorEvents;
	// [ default ] interface _TextEditorEvents
	// [ default, source ] dispinterface _dispTextEditorEvents

	struct __declspec(uuid("23b7a868-6c89-436a-94fa-25d755456a77"))
		_TextEditorEvents : IDispatch
	{};

	struct __declspec(uuid("2699dd44-c507-4da3-aa34-314a6c21dfe2"))
		_dispTextEditorEvents : IDispatch
	{};

	enum __declspec(uuid("e0e643c3-5410-487a-b64e-2664a5c4ab50"))
		vsDisplay
	{
		vsDisplayMDI = 1,
		vsDisplayMDITabs = 2
	};

	struct __declspec(uuid("b35caa8c-77de-4ab3-8e5a-f038e3fc6056"))
		Solution;
	// [ default ] interface _Solution

	enum __declspec(uuid("16386f0e-4ad2-41b8-8d6f-7ff230ae1d17"))
		vsBuildState
	{
		vsBuildStateNotStarted = 1,
		vsBuildStateInProgress = 2,
		vsBuildStateDone = 3
	};

	enum __declspec(uuid("79946991-1ae2-4d2d-9768-fd7801be7daa"))
		vsCommandBarType
	{
		vsCommandBarTypePopup = 10,
		vsCommandBarTypeToolbar = 23,
		vsCommandBarTypeMenu = 24
	};

	struct __declspec(uuid("c34301a1-3ef1-41d8-932a-fea4a8a8ce0c"))
		StatusBar : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall Clear() = 0;
		virtual HRESULT __stdcall Animate(
			VARIANT_BOOL On,
			VARIANT AnimationType) = 0;
		virtual HRESULT __stdcall Progress(
			VARIANT_BOOL InProgress,
			/*[in]*/ BSTR Label,
			/*[in]*/ long AmountCompleted,
			/*[in]*/ long Total) = 0;
		virtual HRESULT __stdcall SetXYWidthHeight(
			long X,
			long Y,
			long Width,
			long Height) = 0;
		virtual HRESULT __stdcall SetLineColumnCharacter(
			long Line,
			long Column,
			long Character) = 0;
		virtual HRESULT __stdcall put_Text(
			/*[in]*/ BSTR pTextc) = 0;
		virtual HRESULT __stdcall get_Text(
			/*[out,retval]*/ BSTR * pTextc) = 0;
		virtual HRESULT __stdcall Highlight(
			VARIANT_BOOL Highlight) = 0;
		virtual HRESULT __stdcall ShowTextUpdates(
			VARIANT_BOOL TextUpdates,
			/*[out,retval]*/ VARIANT_BOOL * WillShowUpdates) = 0;
	};

	struct __declspec(uuid("e57c510b-968b-4a3c-a467-ee4013157dc9"))
		IExtenderSite : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall NotifyDelete(
			/*[in]*/ long Cookie) = 0;
		virtual HRESULT __stdcall GetObject(
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ IDispatch * * ppObject) = 0;
	};

	struct __declspec(uuid("4db06329-23f4-443b-9abd-9cf611e8ae07"))
		IExtenderProvider : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall GetExtender(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[in]*/ IDispatch * ExtendeeObject,
			/*[in]*/ struct IExtenderSite * ExtenderSite,
			/*[in]*/ long Cookie,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall CanExtend(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[in]*/ IDispatch * ExtendeeObject,
			/*[out,retval]*/ VARIANT_BOOL * fRetval) = 0;
	};

	struct __declspec(uuid("f69b64a3-9017-4e48-9784-e152b51aa722"))
		IExtenderProviderUnk : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall GetExtender(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[in]*/ IUnknown * ExtendeeObject,
			/*[in]*/ struct IExtenderSite * ExtenderSite,
			/*[in]*/ long Cookie,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall CanExtend(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[in]*/ IUnknown * ExtendeeObject,
			/*[out,retval]*/ VARIANT_BOOL * fRetval) = 0;
	};

	struct __declspec(uuid("8d0aa9cc-8465-42f3-ad6e-dfde28ccc75d"))
		ObjectExtenders : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall RegisterExtenderProvider(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[in]*/ struct IExtenderProvider * ExtenderProvider,
			/*[in]*/ BSTR LocalizedName,
			/*[out,retval]*/ long * Cookie) = 0;
		virtual HRESULT __stdcall UnregisterExtenderProvider(
			/*[in]*/ long Cookie) = 0;
		virtual HRESULT __stdcall GetExtender(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[in]*/ IUnknown * ExtendeeObject,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall GetExtenderNames(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ IUnknown * ExtendeeObject,
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall GetContextualExtenderCATIDs(
			/*[out,retval]*/ VARIANT * ExtenderCATIDs) = 0;
		virtual HRESULT __stdcall GetLocalizedExtenderName(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[out,retval]*/ BSTR * pLocalizedName) = 0;
		virtual HRESULT __stdcall RegisterExtenderProviderUnk(
			/*[in]*/ BSTR ExtenderCATID,
			/*[in]*/ BSTR ExtenderName,
			/*[in]*/ struct IExtenderProviderUnk * ExtenderProvider,
			/*[in]*/ BSTR LocalizedName,
			/*[out,retval]*/ long * Cookie) = 0;
	};

	enum __declspec(uuid("9fb64132-726a-4262-829e-051c6ec7b24f"))
		vsFindAction
	{
		vsFindActionFind = 1,
		vsFindActionFindAll = 2,
		vsFindActionReplace = 3,
		vsFindActionReplaceAll = 4,
		vsFindActionBookmarkAll = 5
	};

	enum __declspec(uuid("530f2d0e-a5f9-4b7a-a379-0a3b99b5e66d"))
		vsFindPatternSyntax
	{
		vsFindPatternSyntaxLiteral = 0,
		vsFindPatternSyntaxRegExpr = 1,
		vsFindPatternSyntaxWildcards = 2
	};

	enum __declspec(uuid("481899fb-b657-4e45-8c00-0e051052bb13"))
		vsFindTarget
	{
		vsFindTargetCurrentDocument = 1,
		vsFindTargetCurrentDocumentSelection = 2,
		vsFindTargetCurrentDocumentFunction = 3,
		vsFindTargetOpenDocuments = 4,
		vsFindTargetCurrentProject = 5,
		vsFindTargetSolution = 6,
		vsFindTargetFiles = 7
	};

	enum __declspec(uuid("ea9142bf-b2af-48a7-b256-93ccd3350bd8"))
		vsFindResultsLocation
	{
		vsFindResultsNone = 0,
		vsFindResults1 = 1,
		vsFindResults2 = 2
	};

	struct __declspec(uuid("40d4b9b6-739b-4965-8d65-692aec692266"))
		Find : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Action(
			/*[out,retval]*/ enum vsFindAction * pAction) = 0;
		virtual HRESULT __stdcall put_Action(
			/*[in]*/ enum vsFindAction pAction) = 0;
		virtual HRESULT __stdcall get_FindWhat(
			/*[out,retval]*/ BSTR * pFindWhat) = 0;
		virtual HRESULT __stdcall put_FindWhat(
			/*[in]*/ BSTR pFindWhat) = 0;
		virtual HRESULT __stdcall get_MatchCase(
			/*[out,retval]*/ VARIANT_BOOL * pMatchCase) = 0;
		virtual HRESULT __stdcall put_MatchCase(
			/*[in]*/ VARIANT_BOOL pMatchCase) = 0;
		virtual HRESULT __stdcall get_MatchWholeWord(
			/*[out,retval]*/ VARIANT_BOOL * pMatchWholeWord) = 0;
		virtual HRESULT __stdcall put_MatchWholeWord(
			/*[in]*/ VARIANT_BOOL pMatchWholeWord) = 0;
		virtual HRESULT __stdcall get_MatchInHiddenText(
			/*[out,retval]*/ VARIANT_BOOL * pMatchInHiddenText) = 0;
		virtual HRESULT __stdcall put_MatchInHiddenText(
			/*[in]*/ VARIANT_BOOL pMatchInHiddenText) = 0;
		virtual HRESULT __stdcall get_Backwards(
			/*[out,retval]*/ VARIANT_BOOL * pBackwards) = 0;
		virtual HRESULT __stdcall put_Backwards(
			/*[in]*/ VARIANT_BOOL pBackwards) = 0;
		virtual HRESULT __stdcall get_SearchSubfolders(
			/*[out,retval]*/ VARIANT_BOOL * pSearchSubfolders) = 0;
		virtual HRESULT __stdcall put_SearchSubfolders(
			/*[in]*/ VARIANT_BOOL pSearchSubfolders) = 0;
		virtual HRESULT __stdcall get_KeepModifiedDocumentsOpen(
			/*[out,retval]*/ VARIANT_BOOL * pKeepModifiedDocumentsOpen) = 0;
		virtual HRESULT __stdcall put_KeepModifiedDocumentsOpen(
			/*[in]*/ VARIANT_BOOL pKeepModifiedDocumentsOpen) = 0;
		virtual HRESULT __stdcall get_PatternSyntax(
			/*[out,retval]*/ enum vsFindPatternSyntax * pPatternSyntax) = 0;
		virtual HRESULT __stdcall put_PatternSyntax(
			/*[in]*/ enum vsFindPatternSyntax pPatternSyntax) = 0;
		virtual HRESULT __stdcall get_ReplaceWith(
			/*[out,retval]*/ BSTR * pReplaceWith) = 0;
		virtual HRESULT __stdcall put_ReplaceWith(
			/*[in]*/ BSTR pReplaceWith) = 0;
		virtual HRESULT __stdcall get_Target(
			/*[out,retval]*/ enum vsFindTarget * pTarget) = 0;
		virtual HRESULT __stdcall put_Target(
			/*[in]*/ enum vsFindTarget pTarget) = 0;
		virtual HRESULT __stdcall get_SearchPath(
			/*[out,retval]*/ BSTR * pSearchPath) = 0;
		virtual HRESULT __stdcall put_SearchPath(
			/*[in]*/ BSTR pSearchPath) = 0;
		virtual HRESULT __stdcall get_FilesOfType(
			/*[out,retval]*/ BSTR * pFilesOfType) = 0;
		virtual HRESULT __stdcall put_FilesOfType(
			/*[in]*/ BSTR pFilesOfType) = 0;
		virtual HRESULT __stdcall get_ResultsLocation(
			/*[out,retval]*/ enum vsFindResultsLocation * pResultsLocation) = 0;
		virtual HRESULT __stdcall put_ResultsLocation(
			/*[in]*/ enum vsFindResultsLocation pResultsLocation) = 0;
		virtual HRESULT __stdcall Execute(
			/*[out,retval]*/ enum vsFindResult * pResult) = 0;
		virtual HRESULT __stdcall FindReplace(
			/*[in]*/ enum vsFindAction Action,
			/*[in]*/ BSTR FindWhat,
			/*[in]*/ long vsFindOptionsValue,
			/*[in]*/ BSTR ReplaceWith,
			/*[in]*/ enum vsFindTarget Target,
			/*[in]*/ BSTR SearchPath,
			/*[in]*/ BSTR FilesOfType,
			/*[in]*/ enum vsFindResultsLocation ResultsLocation,
			/*[out,retval]*/ enum vsFindResult * pResult) = 0;
	};

	enum __declspec(uuid("d920605a-11dd-4a12-a8dc-10578bb08606"))
		wizardResult
	{
		wizardResultSuccess = -1,
		wizardResultFailure = 0,
		wizardResultCancel = 1,
		wizardResultBackOut = 2
	};

	enum __declspec(uuid("b4cc3e73-0038-47e5-ba52-d1183f8fdbf0"))
		vsNavigateOptions
	{
		vsNavigateOptionsDefault = 0,
		vsNavigateOptionsNewWindow = 1
	};

	enum __declspec(uuid("ca3ee5d0-6305-422f-a425-99737956088f"))
		vsPromptResult
	{
		vsPromptResultYes = 1,
		vsPromptResultNo = 2,
		vsPromptResultCancelled = 3
	};

	struct __declspec(uuid("d8dec44d-caf2-4b39-a539-b91ae921ba92"))
		UndoContext : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall Open(
			/*[in]*/ BSTR Name,
			/*[in]*/ VARIANT_BOOL Strict) = 0;
		virtual HRESULT __stdcall Close() = 0;
		virtual HRESULT __stdcall SetAborted() = 0;
		virtual HRESULT __stdcall get_IsStrict(
			/*[out,retval]*/ VARIANT_BOOL * pIsStrict) = 0;
		virtual HRESULT __stdcall get_IsAborted(
			/*[out,retval]*/ VARIANT_BOOL * pIsAborted) = 0;
		virtual HRESULT __stdcall get_IsOpen(
			/*[out,retval]*/ VARIANT_BOOL * pIsOpen) = 0;
	};

	struct __declspec(uuid("f9f99155-6d4d-49b1-ad63-c78c3e8a5916"))
		Macros : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_IsRecording(
			/*[out,retval]*/ VARIANT_BOOL * vbIsRecording) = 0;
		virtual HRESULT __stdcall EmitMacroCode(
			BSTR Code) = 0;
		virtual HRESULT __stdcall Pause() = 0;
		virtual HRESULT __stdcall Resume() = 0;
	};

	struct __declspec(uuid("f1ddc2c2-df76-4ebb-9de8-48ad2557062c"))
		SourceControl : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * ppDTEObject) = 0;
		virtual HRESULT __stdcall IsItemUnderSCC(
			/*[in]*/ BSTR ItemName,
			/*[out,retval]*/ VARIANT_BOOL * pfControlled) = 0;
		virtual HRESULT __stdcall IsItemCheckedOut(
			/*[in]*/ BSTR ItemName,
			/*[out,retval]*/ VARIANT_BOOL * pfCheckedOut) = 0;
		virtual HRESULT __stdcall CheckOutItem(
			/*[in]*/ BSTR ItemName,
			/*[out,retval]*/ VARIANT_BOOL * pfCheckedOut) = 0;
		virtual HRESULT __stdcall CheckOutItems(
			/*[in]*/ SAFEARRAY * * ItemNames,
			/*[out,retval]*/ VARIANT_BOOL * pfCheckedOut) = 0;
		virtual HRESULT __stdcall ExcludeItem(
			/*[in]*/ BSTR ProjectFile,
			/*[in]*/ BSTR ItemName) = 0;
		virtual HRESULT __stdcall ExcludeItems(
			/*[in]*/ BSTR ProjectFile,
			/*[in]*/ SAFEARRAY * * ItemNames) = 0;
	};

	struct __declspec(uuid("6659ed14-2ab6-47f3-a890-00c8aba43b84"))
		ISupportVSProperties : IUnknown
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall NotifyPropertiesDelete() = 0;
	};

	enum __declspec(uuid("44df8fdc-7378-11d2-97ca-00c04fb6c6ff"))
		vsToolBoxItemFormat
	{
		vsToolBoxItemFormatText = 1,
		vsToolBoxItemFormatHTML = 2,
		vsToolBoxItemFormatGUID = 4,
		vsToolBoxItemFormatDotNETComponent = 8
	};

	struct __declspec(uuid("eb5be8a7-e593-4de6-a923-c2afecb96336"))
		IExtensibleObjectSite : IUnknown
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall NotifyDelete(
			/*[in]*/ IUnknown * punkObj) = 0;
	};

	struct __declspec(uuid("8cc0cde1-c16a-4749-99af-6f7523c34a57"))
		IVsProfferCommands : IUnknown
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall AddNamedCommand(
			/*[in]*/ GUID * pguidPackage,
			/*[in]*/ GUID * pguidCmdGroup,
			/*[in]*/ LPWSTR pszCmdNameCanonical,
			/*[out]*/ unsigned long * pdwCmdId,
			/*[in]*/ LPWSTR pszCmdNameLocalized,
			/*[in]*/ LPWSTR pszBtnText,
			/*[in]*/ LPWSTR pszCmdTooltip,
			/*[in]*/ LPWSTR pszSatelliteDLL,
			/*[in]*/ unsigned long dwBitmapResourceId,
			/*[in]*/ unsigned long dwBitmapImageIndex,
			/*[in]*/ unsigned long dwCmdFlagsDefault,
			/*[in]*/ unsigned long cUIContexts,
			/*[in]*/ GUID * rgguidUIContexts) = 0;
		virtual HRESULT __stdcall RemoveNamedCommand(
			/*[in]*/ LPWSTR pszCmdNameCanonical) = 0;
		virtual HRESULT __stdcall RenameNamedCommand(
			/*[in]*/ LPWSTR pszCmdNameCanonical,
			/*[in]*/ LPWSTR pszCmdNameCanonicalNew,
			/*[in]*/ LPWSTR pszCmdNameLocalizedNew) = 0;
		virtual HRESULT __stdcall AddCommandBarControl(
			/*[in]*/ LPWSTR pszCmdNameCanonical,
			/*[in]*/ IDispatch * pCmdBarParent,
			/*[in]*/ unsigned long dwIndex,
			/*[in]*/ unsigned long dwCmdType,
			/*[out]*/ IDispatch * * ppCmdBarCtrl) = 0;
		virtual HRESULT __stdcall RemoveCommandBarControl(
			/*[in]*/ IDispatch * pCmdBarCtrl) = 0;
		virtual HRESULT __stdcall AddCommandBar(
			/*[in]*/ LPWSTR pszCmdBarName,
			/*[in]*/ enum vsCommandBarType dwType,
			/*[in]*/ IDispatch * pCmdBarParent,
			/*[in]*/ unsigned long dwIndex,
			/*[out]*/ IDispatch * * ppCmdBar) = 0;
		virtual HRESULT __stdcall RemoveCommandBar(
			/*[in]*/ IDispatch * pCmdBar) = 0;
		virtual HRESULT __stdcall FindCommandBar(
			/*[in]*/ void * pToolbarSet,
			/*[in]*/ GUID * pguidCmdGroup,
			/*[in]*/ unsigned long dwMenuId,
			/*[out,retval]*/ IDispatch * * ppdispCmdBar) = 0;
	};

	enum __declspec(uuid("221ab797-408b-4d69-9dd1-c6f329189414"))
		vsFilterProperties
	{
		vsFilterPropertiesNone = 0,
		vsFilterPropertiesAll = 1,
		vsFilterPropertiesSet = 2
	};

	struct __declspec(uuid("aade1f59-6ace-43d1-8fca-42af3a5c4f3c"))
		IFilterProperties : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall IsPropertyHidden(
			/*[in]*/ BSTR PropertyName,
			/*[out,retval]*/ enum vsFilterProperties * pRetval) = 0;
	};

	enum __declspec(uuid("28362492-b1e3-4998-94c6-0a2cdb5484ed"))
		vsUISelectionType
	{
		vsUISelectionTypeSelect = 1,
		vsUISelectionTypeToggle = 2,
		vsUISelectionTypeExtend = 3,
		vsUISelectionTypeSetCaret = 4
	};

	struct __declspec(uuid("9ff3ddca-1795-4191-a5b1-02d1ae35d074"))
		TextEditor : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Application(
			/*[out,retval]*/ struct _DTE * * ppApp) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * ppParent) = 0;
		virtual HRESULT __stdcall get_Emulation(
			/*[out,retval]*/ long * pEditorType) = 0;
		virtual HRESULT __stdcall put_Emulation(
			/*[in]*/ long pEditorType) = 0;
		virtual HRESULT __stdcall get_Overtype(
			/*[out,retval]*/ VARIANT_BOOL * pbOT) = 0;
		virtual HRESULT __stdcall put_Overtype(
			/*[in]*/ VARIANT_BOOL pbOT) = 0;
		virtual HRESULT __stdcall get_VisibleWhitespace(
			/*[out,retval]*/ VARIANT_BOOL * pbVW) = 0;
		virtual HRESULT __stdcall put_VisibleWhitespace(
			/*[in]*/ VARIANT_BOOL pbVW) = 0;
	};
	const LPSTR vsCMLanguageVC = (char*) "{B5E9BD32-6D3E-4B5D-925E-8A43B79820B4}";
	const LPSTR vsCMLanguageVB = (char*) "{B5E9BD33-6D3E-4B5D-925E-8A43B79820B4}";
	const LPSTR vsCMLanguageCSharp = (char*) "{B5E9BD34-6D3E-4B5D-925E-8A43B79820B4}";
	const LPSTR vsCMLanguageIDL = (char*) "{B5E9BD35-6D3E-4B5D-925E-8A43B79820B4}";
	const LPSTR vsCMLanguageMC = (char*) "{B5E9BD36-6D3E-4B5D-925E-8A43B79820B4}";

	enum __declspec(uuid("ed1a3f98-4477-11d3-89bf-00c04f688dde"))
		vsCMPrototype
	{
		vsCMPrototypeFullname = 1,
		vsCMPrototypeNoName = 2,
		vsCMPrototypeClassName = 4,
		vsCMPrototypeParamTypes = 8,
		vsCMPrototypeParamNames = 16,
		vsCMPrototypeParamDefaultValues = 32,
		vsCMPrototypeUniqueSignature = 64,
		vsCMPrototypeType = 128,
		vsCMPrototypeInitExpression = 256
	};

	struct __declspec(uuid("d4bb39fb-0f0e-11d3-b880-00c04f79e479"))
		_DebuggerEventsRoot : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DebuggerEvents(
			/*[out,retval]*/ IDispatch * * disp) = 0;
	};

	enum __declspec(uuid("178c767a-1963-4bc5-b3f2-e70f3eb441a8"))
		vsNavigateBrowser
	{
		vsNavigateBrowserDefault = 0,
		vsNavigateBrowserHelp = 1,
		vsNavigateBrowserNewWindow = 2
	};

	enum __declspec(uuid("947978f5-185b-4366-921e-a14c0ed46702"))
		vsCommandDisabledFlags
	{
		vsCommandDisabledFlagsEnabled = 0,
		vsCommandDisabledFlagsGrey = 16,
		vsCommandDisabledFlagsHidden = 32
	};

	enum __declspec(uuid("9e3c8489-ae44-4fcf-9697-cd3f312ba08c"))
		vsInitializeMode
	{
		vsInitializeModeStartup = 0,
		vsInitializeModeReset = 1
	};

	struct __declspec(uuid("e914bbe1-03a4-11d1-bbcd-00a0c90f2744"))
		IDTWizard : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Execute(
			/*[in]*/ IDispatch * Application,
			/*[in]*/ long hwndOwner,
			/*[in]*/ SAFEARRAY * * ContextParams,
			/*[in]*/ SAFEARRAY * * CustomParams,
			/*[in,out]*/ enum wizardResult * retval) = 0;
	};

	struct __declspec(uuid("e2cc506a-588b-4f65-a1f0-2244c060abcb"))
		IVsGlobalsCallback : IUnknown
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall WriteVariablesToData(
			/*[in]*/ LPWSTR pVariableName,
			/*[in]*/ VARIANT * varData) = 0;
		virtual HRESULT __stdcall ReadData(
			/*[in]*/ struct Globals * pGlobals) = 0;
		virtual HRESULT __stdcall ClearVariables() = 0;
		virtual HRESULT __stdcall VariableChanged() = 0;
		virtual HRESULT __stdcall CanModifySource() = 0;
		virtual HRESULT __stdcall GetParent(
			IDispatch * * ppOut) = 0;
	};

	struct __declspec(uuid("192ac688-e7c6-4f9d-8180-4b37efbf6f3a"))
		IVsGlobals : IUnknown
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Load() = 0;
		virtual HRESULT __stdcall Save() = 0;
		virtual HRESULT __stdcall Empty() = 0;
	};

	enum __declspec(uuid("d9e97957-b425-4cd6-bb74-9e6f232a342a"))
		vsCommandStatusTextWanted
	{
		vsCommandStatusTextWantedNone = 0,
		vsCommandStatusTextWantedName = 1,
		vsCommandStatusTextWantedStatus = 2
	};

	enum __declspec(uuid("e0ace2e3-49c1-4aa3-ac03-a2877a93d77f"))
		vsCommandStatus
	{
		vsCommandStatusUnsupported = 0,
		vsCommandStatusSupported = 1,
		vsCommandStatusEnabled = 2,
		vsCommandStatusLatched = 4,
		vsCommandStatusNinched = 8,
		vsCommandStatusInvisible = 16
	};

	enum __declspec(uuid("3c537e1a-39b8-4fda-9f22-93d8c030865d"))
		vsCommandExecOption
	{
		vsCommandExecOptionDoDefault = 0,
		vsCommandExecOptionPromptUser = 1,
		vsCommandExecOptionDoPromptUser = 2,
		vsCommandExecOptionShowHelp = 3
	};

	struct __declspec(uuid("7ef39a3e-590d-4879-88d4-c9be5bcfd92e"))
		IDTCommandTarget : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall QueryStatus(
			/*[in]*/ BSTR CmdName,
			/*[in]*/ enum vsCommandStatusTextWanted NeededText,
			/*[in,out]*/ enum vsCommandStatus * StatusOption,
			/*[in,out]*/ VARIANT * CommandText) = 0;
		virtual HRESULT __stdcall Exec(
			/*[in]*/ BSTR CmdName,
			/*[in]*/ enum vsCommandExecOption ExecuteOption,
			/*[in]*/ VARIANT * VariantIn,
			/*[in,out]*/ VARIANT * VariantOut,
			/*[in,out]*/ VARIANT_BOOL * Handled) = 0;
	};

	struct __declspec(uuid("85451f83-b5ca-437f-a619-0cb705707420"))
		_ProjectsEvents : IDispatch
	{};

	struct __declspec(uuid("7f508d55-627f-4d7f-be0b-9e3d829ff0ed"))
		_dispProjectsEvents : IDispatch
	{};

	struct __declspec(uuid("536a4be3-a376-408e-954c-471c779e216f"))
		ProjectsEvents;
	// [ default ] interface _ProjectsEvents
	// [ default, source ] dispinterface _dispProjectsEvents

	struct __declspec(uuid("7658b944-f37b-11d2-aacf-00c04f688dde"))
		_MiscSlnFilesEventsRoot : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_SolutionItemsEvents(
			/*[out,retval]*/ IDispatch * * ppeNew) = 0;
		virtual HRESULT __stdcall get_MiscFilesEvents(
			/*[out,retval]*/ IDispatch * * ppeNew) = 0;
	};

	struct __declspec(uuid("9e66fe98-a1c6-421d-8c0c-6da4e652e770"))
		_CommandBarControlEvents : IDispatch
	{};

	struct __declspec(uuid("987fb893-f96d-11d0-bbbb-00a0c90f2744"))
		_dispCommandBarControlEvents : IDispatch
	{};

	struct __declspec(uuid("bfd4b2b2-9eec-4db8-aba0-ac316f4c7328"))
		CommandBarEvents;
	// [ default ] interface _CommandBarControlEvents
	// [ default, source ] dispinterface _dispCommandBarControlEvents

	enum __declspec(uuid("3982fa28-7bcc-4f14-817c-93eeece8beee"))
		vsBuildKind
	{
		vsBuildKindSolution = 0,
		vsBuildKindProject = 1,
		vsBuildKindProjectItem = 2
	};

	enum __declspec(uuid("11f0cb7f-4f63-4e09-b99c-92a6cc5f84ce"))
		vsTextChanged
	{
		vsTextChangedMultiLine = 1,
		vsTextChangedSave = 2,
		vsTextChangedCaretMoved = 4,
		vsTextChangedReplaceAll = 8,
		vsTextChangedNewline = 16,
		vsTextChangedFindStarting = 32
	};

	struct __declspec(uuid("aa6f4085-33b6-4629-b9ea-692101007cc2"))
		_OutputWindowEventsRoot : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_OutputWindowEvents(
			/*[in]*/ BSTR PaneFilter,
			/*[out,retval]*/ IDispatch * * ppDisp) = 0;
	};

	struct __declspec(uuid("6bc8c372-c6f0-4be6-b255-827ac190bf71"))
		_TaskListEventsRoot : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_TaskListEvents(
			/*[out,retval]*/ IDispatch * * ppDisp) = 0;
	};

	enum __declspec(uuid("f40a5d39-fd39-4f03-8358-4df3c338b448"))
		vsStatusAnimation
	{
		vsStatusAnimationGeneral = 0,
		vsStatusAnimationPrint = 1,
		vsStatusAnimationSave = 2,
		vsStatusAnimationDeploy = 3,
		vsStatusAnimationSync = 4,
		vsStatusAnimationBuild = 5,
		vsStatusAnimationFind = 6
	};

	struct __declspec(uuid("bdcaf240-2692-4713-902a-b110b1d0f100"))
		IDTToolsOptionsPage : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall OnAfterCreated(
			/*[in]*/ struct _DTE * DTEObject) = 0;
		virtual HRESULT __stdcall GetProperties(
			/*[in,out]*/ IDispatch * * PropertiesObject) = 0;
		virtual HRESULT __stdcall OnOK() = 0;
		virtual HRESULT __stdcall OnCancel() = 0;
		virtual HRESULT __stdcall OnHelp() = 0;
	};

	enum __declspec(uuid("705cd8ac-d29b-11d2-aabd-00c04f688dde"))
		vsStartUp
	{
		vsStartUpShowHomePage = 0,
		vsStartUpLoadLastSolution = 1,
		vsStartUpOpenProjectDialog = 2,
		vsStartUpNewProjectDialog = 3,
		vsStartUpEmptyEnvironment = 4
	};

	enum __declspec(uuid("705cd8a4-d29b-11d2-aabd-00c04f688dde"))
		vsFontCharSet
	{
		vsFontCharSetANSI = 0,
		vsFontCharSetDefault = 1,
		vsFontCharSetSymbol = 2,
		vsFontCharSetShiftJIS = 128,
		vsFontCharSetHangeul = 129,
		vsFontCharSetGB2312 = 134,
		vsFontCharSetChineseBig5 = 136,
		vsFontCharSetOEM = 255,
		vsFontCharSetJohab = 130,
		vsFontCharSetHebrew = 177,
		vsFontCharSetArabic = 178,
		vsFontCharSetGreek = 161,
		vsFontCharSetTurkish = 162,
		vsFontCharSetVietnamese = 163,
		vsFontCharSetThai = 222,
		vsFontCharSetEastEurope = 238,
		vsFontCharSetRussian = 204,
		vsFontCharSetMac = 77,
		vsFontCharSetBaltic = 186
	};

	enum __declspec(uuid("947ffc03-8314-4288-bdd7-dfd282da40d5"))
		vsBrowserViewSource
	{
		vsBrowserViewSourceSource = 1,
		vsBrowserViewSourceDesign = 2,
		vsBrowserViewSourceExternal = 3
	};

	struct __declspec(uuid("a3286b03-5ac6-44f0-8cc3-ebed7f1124e5"))
		_EnvironmentWebBrowser : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_UseDefaultHomePage(
			VARIANT_BOOL pUseDefault) = 0;
		virtual HRESULT __stdcall get_UseDefaultHomePage(
			/*[out,retval]*/ VARIANT_BOOL * pUseDefault) = 0;
		virtual HRESULT __stdcall put_HomePage(
			BSTR URL) = 0;
		virtual HRESULT __stdcall get_HomePage(
			/*[out,retval]*/ BSTR * URL) = 0;
		virtual HRESULT __stdcall put_UseDefaultSearchPage(
			VARIANT_BOOL pUseDefault) = 0;
		virtual HRESULT __stdcall get_UseDefaultSearchPage(
			/*[out,retval]*/ VARIANT_BOOL * pUseDefault) = 0;
		virtual HRESULT __stdcall put_SearchPage(
			BSTR URL) = 0;
		virtual HRESULT __stdcall get_SearchPage(
			/*[out,retval]*/ BSTR * URL) = 0;
		virtual HRESULT __stdcall put_ViewSourceIn(
			enum vsBrowserViewSource Location) = 0;
		virtual HRESULT __stdcall get_ViewSourceIn(
			/*[out,retval]*/ enum vsBrowserViewSource * Location) = 0;
		virtual HRESULT __stdcall put_ViewSourceExternalProgram(
			BSTR Path) = 0;
		virtual HRESULT __stdcall get_ViewSourceExternalProgram(
			/*[out,retval]*/ BSTR * Path) = 0;
	};

	struct __declspec(uuid("aebded64-a206-11d3-b8b5-00c04f79f802"))
		_EnvironmentHelp : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_PreferredLanguage(
			/*[out,retval]*/ long * LCID) = 0;
		virtual HRESULT __stdcall put_PreferredLanguage(
			/*[in]*/ long LCID) = 0;
		virtual HRESULT __stdcall get_PreferredCollection(
			/*[out,retval]*/ BSTR * Namespace) = 0;
		virtual HRESULT __stdcall put_PreferredCollection(
			/*[in]*/ BSTR Namespace) = 0;
		virtual HRESULT __stdcall get_External(
			/*[out,retval]*/ VARIANT_BOOL * ExternalHelp) = 0;
		virtual HRESULT __stdcall put_External(
			/*[in]*/ VARIANT_BOOL ExternalHelp) = 0;
	};

	struct __declspec(uuid("e5d17051-d6e5-4da7-8b3a-ca888617a5e7"))
		ColorableItems : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_Foreground(
			/*[out,retval]*/ OLE_COLOR * pColor) = 0;
		virtual HRESULT __stdcall put_Foreground(
			OLE_COLOR pColor) = 0;
		virtual HRESULT __stdcall get_Background(
			/*[out,retval]*/ OLE_COLOR * pColor) = 0;
		virtual HRESULT __stdcall put_Background(
			OLE_COLOR pColor) = 0;
		virtual HRESULT __stdcall get_Bold(
			/*[out,retval]*/ VARIANT_BOOL * Bold) = 0;
		virtual HRESULT __stdcall put_Bold(
			VARIANT_BOOL Bold) = 0;
	};

	struct __declspec(uuid("f25ae7e6-1460-4ba4-8e5e-bbbe746de353"))
		FontsAndColorsItems : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct ColorableItems * * pFontsAndColorsItem) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
	};

	struct __declspec(uuid("256068f6-1add-4f7b-ba76-571314c413ad"))
		_FontsAndColors : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_FontFamily(
			/*[out,retval]*/ BSTR * pFamily) = 0;
		virtual HRESULT __stdcall put_FontFamily(
			BSTR pFamily) = 0;
		virtual HRESULT __stdcall get_FontCharacterSet(
			/*[out,retval]*/ enum vsFontCharSet * pFontCharSet) = 0;
		virtual HRESULT __stdcall put_FontCharacterSet(
			enum vsFontCharSet pFontCharSet) = 0;
		virtual HRESULT __stdcall get_FontSize(
			/*[out,retval]*/ short * pSize) = 0;
		virtual HRESULT __stdcall put_FontSize(
			short pSize) = 0;
		virtual HRESULT __stdcall get_FontsAndColorsItems(
			/*[out,retval]*/ struct FontsAndColorsItems * * ppColorableItems) = 0;
	};

	struct __declspec(uuid("48e61d9c-8c8d-42d3-914b-46d70c8b7a40"))
		_EnvironmentGeneral : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_OnStartUp(
			/*[in]*/ enum vsStartUp pstartup) = 0;
		virtual HRESULT __stdcall get_OnStartUp(
			/*[out,retval]*/ enum vsStartUp * pstartup) = 0;
		virtual HRESULT __stdcall put_ShowStatusBar(
			/*[in]*/ VARIANT_BOOL pfshow) = 0;
		virtual HRESULT __stdcall get_ShowStatusBar(
			/*[out,retval]*/ VARIANT_BOOL * pfshow) = 0;
		virtual HRESULT __stdcall put_WindowMenuContainsNItems(
			/*[in]*/ long plCount) = 0;
		virtual HRESULT __stdcall get_WindowMenuContainsNItems(
			/*[out,retval]*/ long * plCount) = 0;
		virtual HRESULT __stdcall put_MRUListContainsNItems(
			/*[in]*/ long plCount) = 0;
		virtual HRESULT __stdcall get_MRUListContainsNItems(
			/*[out,retval]*/ long * plCount) = 0;
		virtual HRESULT __stdcall get_AnimationSpeed(
			/*[out,retval]*/ short * pSpeed) = 0;
		virtual HRESULT __stdcall put_AnimationSpeed(
			short pSpeed) = 0;
		virtual HRESULT __stdcall get_Animations(
			/*[out,retval]*/ VARIANT_BOOL * pAnimations) = 0;
		virtual HRESULT __stdcall put_Animations(
			VARIANT_BOOL pAnimations) = 0;
		virtual HRESULT __stdcall get_ShowCommandWindowCompletion(
			/*[out,retval]*/ VARIANT_BOOL * pComplete) = 0;
		virtual HRESULT __stdcall put_ShowCommandWindowCompletion(
			VARIANT_BOOL pComplete) = 0;
		virtual HRESULT __stdcall put_CloseButtonActiveTabOnly(
			VARIANT_BOOL CloseActiveOnly) = 0;
		virtual HRESULT __stdcall get_CloseButtonActiveTabOnly(
			/*[out,retval]*/ VARIANT_BOOL * CloseActiveOnly) = 0;
		virtual HRESULT __stdcall put_AutohidePinActiveTabOnly(
			VARIANT_BOOL AutohidePinActiveOnly) = 0;
		virtual HRESULT __stdcall get_AutohidePinActiveTabOnly(
			/*[out,retval]*/ VARIANT_BOOL * AutohidePinActiveOnly) = 0;
	};

	struct __declspec(uuid("f809cab6-2c9f-41f2-a5af-e26fb80e62ad"))
		_EnvironmentFontsAndColors : IDispatch
	{};

	struct __declspec(uuid("478f06d4-5d57-473f-9b74-5f8e88efa5e7"))
		_EnvironmentProjectsAndSolution : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_OnRunOrPreview(
			/*[in]*/ enum vsSaveChanges pbld) = 0;
		virtual HRESULT __stdcall get_OnRunOrPreview(
			/*[out,retval]*/ enum vsSaveChanges * pbld) = 0;
		virtual HRESULT __stdcall put_ProjectsLocation(
			/*[in]*/ BSTR pLocation) = 0;
		virtual HRESULT __stdcall get_ProjectsLocation(
			/*[out,retval]*/ BSTR * pLocation) = 0;
		virtual HRESULT __stdcall put_ShowOutputWindowBeforeBuild(
			/*[in]*/ VARIANT_BOOL pfshow) = 0;
		virtual HRESULT __stdcall get_ShowOutputWindowBeforeBuild(
			/*[out,retval]*/ VARIANT_BOOL * pfshow) = 0;
		virtual HRESULT __stdcall put_ShowTaskListAfterBuild(
			/*[in]*/ VARIANT_BOOL pfshow) = 0;
		virtual HRESULT __stdcall get_ShowTaskListAfterBuild(
			/*[out,retval]*/ VARIANT_BOOL * pfshow) = 0;
	};

	struct __declspec(uuid("76ed1c48-ed86-4e9e-acf8-a40e765daf25"))
		_EnvironmentDocuments : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_ReuseSavedActiveDocWindow(
			VARIANT_BOOL pReuse) = 0;
		virtual HRESULT __stdcall get_ReuseSavedActiveDocWindow(
			/*[out,retval]*/ VARIANT_BOOL * pReuse) = 0;
		virtual HRESULT __stdcall put_DetectFileChangesOutsideIDE(
			/*[in]*/ VARIANT_BOOL pfAutoRead) = 0;
		virtual HRESULT __stdcall get_DetectFileChangesOutsideIDE(
			/*[out,retval]*/ VARIANT_BOOL * pfAutoRead) = 0;
		virtual HRESULT __stdcall put_AutoloadExternalChanges(
			/*[in]*/ VARIANT_BOOL pfAutoload) = 0;
		virtual HRESULT __stdcall get_AutoloadExternalChanges(
			/*[out,retval]*/ VARIANT_BOOL * pfAutoload) = 0;
		virtual HRESULT __stdcall put_InitializeOpenFileFromCurrentDocument(
			/*[in]*/ VARIANT_BOOL pfInit) = 0;
		virtual HRESULT __stdcall get_InitializeOpenFileFromCurrentDocument(
			/*[out,retval]*/ VARIANT_BOOL * pfInit) = 0;
		virtual HRESULT __stdcall put_MiscFilesProjectSavesLastNItems(
			/*[in]*/ long plCount) = 0;
		virtual HRESULT __stdcall get_MiscFilesProjectSavesLastNItems(
			/*[out,retval]*/ long * plCount) = 0;
		virtual HRESULT __stdcall get_FindReplaceShowMessageBoxes(
			/*[out,retval]*/ VARIANT_BOOL * pShow) = 0;
		virtual HRESULT __stdcall put_FindReplaceShowMessageBoxes(
			VARIANT_BOOL pShow) = 0;
		virtual HRESULT __stdcall get_FindReplaceInitializeFromEditor(
			/*[out,retval]*/ VARIANT_BOOL * pShow) = 0;
		virtual HRESULT __stdcall put_FindReplaceInitializeFromEditor(
			VARIANT_BOOL pShow) = 0;
	};

	struct __declspec(uuid("4bc18a5b-dbb6-4af5-a443-2e3f19365304"))
		_EnvironmentTaskList : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_ConfirmTaskDeletion(
			/*[out,retval]*/ VARIANT_BOOL * pConfirm) = 0;
		virtual HRESULT __stdcall put_ConfirmTaskDeletion(
			VARIANT_BOOL pConfirm) = 0;
		virtual HRESULT __stdcall get_WarnOnAddingHiddenItem(
			/*[out,retval]*/ VARIANT_BOOL * pWarn) = 0;
		virtual HRESULT __stdcall put_WarnOnAddingHiddenItem(
			VARIANT_BOOL pWarn) = 0;
		virtual HRESULT __stdcall get_CommentTokens(
			/*[out,retval]*/ VARIANT * pTokens) = 0;
		virtual HRESULT __stdcall put_CommentTokens(
			VARIANT pTokens) = 0;
	};

	struct __declspec(uuid("9c722678-490d-408f-98ae-b6b9a68aa45d"))
		_EnvironmentKeyboard : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_Scheme(
			/*[in]*/ BSTR pbstr) = 0;
		virtual HRESULT __stdcall get_Scheme(
			/*[out,retval]*/ BSTR * pbstr) = 0;
	};

	struct __declspec(uuid("86c31347-5b52-4715-b454-a6e5fcab975d"))
		IExtensibleObject : IUnknown
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall GetAutomationObject(
			/*[in]*/ BSTR Name,
			/*[in]*/ struct IExtensibleObjectSite * pParent,
			/*[out]*/ IDispatch * * ppDisp) = 0;
	};
	const LPSTR vsTaskCategoryBuildCompile = (char*) "BuildCompile";
	const LPSTR vsTaskCategoryComment = (char*) "Comment";
	const LPSTR vsTaskCategoryShortcut = (char*) "Shortcut";
	const LPSTR vsTaskCategoryUser = (char*) "User";
	const LPSTR vsTaskCategoryMisc = (char*) "Misc";
	const LPSTR vsTaskCategoryHTML = (char*) "HTML";
	const LPSTR vsDocumentKindText = (char*) "{8E7B96A8-E33D-11D0-A6D5-00C04FB67F6A}";
	const LPSTR vsDocumentKindHTML = (char*) "{C76D83F8-A489-11D0-8195-00A0C91BBEE3}";
	const LPSTR vsDocumentKindResource = (char*) "{00000000-0000-0000-0000-000000000000}";
	const LPSTR vsDocumentKindBinary = (char*) "{25834150-CD7E-11D0-92DF-00A0C9138C45}";
	const LPSTR vsViewKindPrimary = (char*) "{00000000-0000-0000-0000-000000000000}";
	const LPSTR vsViewKindAny = (char*) "{FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF}";
	const LPSTR vsViewKindDebugging = (char*) "{7651A700-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsViewKindCode = (char*) "{7651A701-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsViewKindDesigner = (char*) "{7651A702-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsViewKindTextView = (char*) "{7651A703-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsWindowKindTaskList = (char*) "{4A9B7E51-AA16-11D0-A8C5-00A0C921A4D2}";
	const LPSTR vsWindowKindToolbox = (char*) "{B1E99781-AB81-11D0-B683-00AA00A3EE26}";
	const LPSTR vsWindowKindCallStack = (char*) "{0504FF91-9D61-11D0-A794-00A0C9110051}";
	const LPSTR vsWindowKindThread = (char*) "{E62CE6A0-B439-11D0-A79D-00A0C9110051}";
	const LPSTR vsWindowKindLocals = (char*) "{4A18F9D0-B838-11D0-93EB-00A0C90F2734}";
	const LPSTR vsWindowKindAutoLocals = (char*) "{F2E84780-2AF1-11D1-A7FA-00A0C9110051}";
	const LPSTR vsWindowKindWatch = (char*) "{90243340-BD7A-11D0-93EF-00A0C90F2734}";
	const LPSTR vsWindowKindProperties = (char*) "{EEFA5220-E298-11D0-8F78-00A0C9110057}";
	const LPSTR vsWindowKindSolutionExplorer = (char*) "{3AE79031-E1BC-11D0-8F78-00A0C9110057}";
	const LPSTR vsWindowKindOutput = (char*) "{34E76E81-EE4A-11D0-AE2E-00A0C90FFFC3}";
	const LPSTR vsWindowKindObjectBrowser = (char*) "{269A02DC-6AF8-11D3-BDC4-00C04F688E50}";
	const LPSTR vsWindowKindMacroExplorer = (char*) "{07CD18B4-3BA1-11D2-890A-0060083196C6}";
	const LPSTR vsWindowKindDynamicHelp = (char*) "{66DBA47C-61DF-11D2-AA79-00C04F990343}";
	const LPSTR vsWindowKindClassView = (char*) "{C9C0AE26-AA77-11D2-B3F0-0000F87570EE}";
	const LPSTR vsWindowKindResourceView = (char*) "{2D7728C2-DE0A-45b5-99AA-89B609DFDE73}";
	const LPSTR vsWindowKindDocumentOutline = (char*) "{25F7E850-FFA1-11D0-B63F-00A0C922E851}";
	const LPSTR vsWindowKindServerExplorer = (char*) "{74946827-37A0-11D2-A273-00C04F8EF4FF}";
	const LPSTR vsWindowKindCommandWindow = (char*) "{28836128-FC2C-11D2-A433-00C04F72D18A}";
	const LPSTR vsWindowKindFindSymbol = (char*) "{53024D34-0EF5-11D3-87E0-00C04F7971A5}";
	const LPSTR vsWindowKindFindSymbolResults = (char*) "{68487888-204A-11D3-87EB-00C04F7971A5}";
	const LPSTR vsWindowKindFindReplace = (char*) "{CF2DDC32-8CAD-11D2-9302-005345000000}";
	const LPSTR vsWindowKindFindResults1 = (char*) "{0F887920-C2B6-11D2-9375-0080C747D9A0}";
	const LPSTR vsWindowKindFindResults2 = (char*) "{0F887921-C2B6-11D2-9375-0080C747D9A0}";
	const LPSTR vsWindowKindMainWindow = (char*) "{9DDABE98-1D02-11D3-89A1-00C04F688DDE}";
	const LPSTR vsWindowKindLinkedWindowFrame = (char*) "{9DDABE99-1D02-11D3-89A1-00C04F688DDE}";
	const LPSTR vsWindowKindWebBrowser = (char*) "{E8B06F52-6D01-11D2-AA7D-00C04F990343}";
	const LPSTR vsWizardAddSubProject = (char*) "{0F90E1D2-4999-11D1-B6D1-00A0C90F2744}";
	const LPSTR vsWizardAddItem = (char*) "{0F90E1D1-4999-11D1-B6D1-00A0C90F2744}";
	const LPSTR vsWizardNewProject = (char*) "{0F90E1D0-4999-11D1-B6D1-00A0C90F2744}";
	const LPSTR vsProjectKindMisc = (char*) "{66A2671D-8FB5-11D2-AA7E-00C04F688DDE}";
	const LPSTR vsProjectItemsKindMisc = (char*) "{66A2671E-8FB5-11D2-AA7E-00C04F688DDE}";
	const LPSTR vsProjectItemKindMisc = (char*) "{66A2671F-8FB5-11D2-AA7E-00C04F688DDE}";
	const LPSTR vsProjectKindUnmodeled = (char*) "{67294A52-A4F0-11D2-AA88-00C04F688DDE}";
	const LPSTR vsProjectKindSolutionItems = (char*) "{66A26720-8FB5-11D2-AA7E-00C04F688DDE}";
	const LPSTR vsProjectItemsKindSolutionItems = (char*) "{66A26721-8FB5-11D2-AA7E-00C04F688DDE}";
	const LPSTR vsProjectItemKindSolutionItems = (char*) "{66A26722-8FB5-11D2-AA7E-00C04F688DDE}";
	const LPSTR vsProjectsKindSolution = (char*) "{96410B9F-3542-4A14-877F-BC7227B51D3B}";
	const LPSTR vsAddInCmdGroup = (char*) "{1E58696E-C90F-11D2-AAB2-00C04F688DDE}";
	const LPSTR vsContextSolutionBuilding = (char*) "{ADFC4E60-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextDebugging = (char*) "{ADFC4E61-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextFullScreenMode = (char*) "{ADFC4E62-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextDesignMode = (char*) "{ADFC4E63-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextNoSolution = (char*) "{ADFC4E64-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextEmptySolution = (char*) "{ADFC4E65-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextSolutionHasSingleProject = (char*) "{ADFC4E66-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextSolutionHasMultipleProjects = (char*) "{93694FA0-0397-11D1-9F4E-00A0C911004F}";
	const LPSTR vsContextMacroRecording = (char*) "{04BBF6A5-4697-11D2-890E-0060083196C6}";
	const LPSTR vsContextMacroRecordingToolbar = (char*) "{85A70471-270A-11D2-88F9-0060083196C6}";
	const LPSTR vsMiscFilesProjectUniqueName = (char*) "<MiscFiles>";
	const LPSTR vsSolutionItemsProjectUniqueName = (char*) "<SolnItems>";
	const LPSTR vsProjectItemKindPhysicalFile = (char*) "{6BB5F8EE-4483-11D3-8BCF-00C04F8EC28C}";
	const LPSTR vsProjectItemKindPhysicalFolder = (char*) "{6BB5F8EF-4483-11D3-8BCF-00C04F8EC28C}";
	const LPSTR vsProjectItemKindVirtualFolder = (char*) "{6BB5F8F0-4483-11D3-8BCF-00C04F8EC28C}";
	const LPSTR vsProjectItemKindSubProject = (char*) "{EA6618E8-6E24-4528-94BE-6889FE16485C}";
	const LPSTR vsext_vk_Primary = (char*) "{00000000-0000-0000-0000-000000000000}";
	const LPSTR vsext_vk_Debugging = (char*) "{7651A700-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsext_vk_Code = (char*) "{7651A701-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsext_vk_Designer = (char*) "{7651A702-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsext_vk_TextView = (char*) "{7651A703-06E5-11D1-8EBD-00A0C90F26EA}";
	const LPSTR vsext_wk_TaskList = (char*) "{4A9B7E51-AA16-11D0-A8C5-00A0C921A4D2}";
	const LPSTR vsext_wk_Toolbox = (char*) "{B1E99781-AB81-11D0-B683-00AA00A3EE26}";
	const LPSTR vsext_wk_CallStackWindow = (char*) "{0504FF91-9D61-11D0-A794-00A0C9110051}";
	const LPSTR vsext_wk_ThreadWindow = (char*) "{E62CE6A0-B439-11D0-A79D-00A0C9110051}";
	const LPSTR vsext_wk_LocalsWindow = (char*) "{4A18F9D0-B838-11D0-93EB-00A0C90F2734}";
	const LPSTR vsext_wk_AutoLocalsWindow = (char*) "{F2E84780-2AF1-11D1-A7FA-00A0C9110051}";
	const LPSTR vsext_wk_WatchWindow = (char*) "{90243340-BD7A-11D0-93EF-00A0C90F2734}";
	const LPSTR vsext_wk_ImmedWindow = (char*) "{98731960-965C-11D0-A78F-00A0C9110051}";
	const LPSTR vsext_wk_PropertyBrowser = (char*) "{EEFA5220-E298-11D0-8F78-00A0C9110057}";
	const LPSTR vsext_wk_SProjectWindow = (char*) "{3AE79031-E1BC-11D0-8F78-00A0C9110057}";
	const LPSTR vsext_wk_OutputWindow = (char*) "{34E76E81-EE4A-11D0-AE2E-00A0C90FFFC3}";
	const LPSTR vsext_wk_ObjectBrowser = (char*) "{269A02DC-6AF8-11D3-BDC4-00C04F688E50}";
	const LPSTR vsext_wk_ContextWindow = (char*) "{66DBA47C-61DF-11D2-AA79-00C04F990343}";
	const LPSTR vsext_wk_ClassView = (char*) "{C9C0AE26-AA77-11D2-B3F0-0000F87570EE}";
	const LPSTR vsext_GUID_AddItemWizard = (char*) "{0F90E1D1-4999-11D1-B6D1-00A0C90F2744}";
	const LPSTR vsext_GUID_NewProjectWizard = (char*) "{0F90E1D0-4999-11D1-B6D1-00A0C90F2744}";
	const LPSTR dsCPP = (char*) "C/C++";
	const LPSTR dsHTML_IE3 = (char*) "HTML - IE 3.0";
	const LPSTR dsHTML_RFC1866 = (char*) "HTML 2.0 (RFC 1866)";
	const LPSTR dsFortran_Fixed = (char*) "Fortran Fixed";
	const LPSTR dsFortran_Free = (char*) "Fortran Free";
	const LPSTR dsJava = (char*) "Java";
	const LPSTR dsVBSMacro = (char*) "VBS Macro";
	const LPSTR dsIDL = (char*) "ODL/IDL";
	const LPSTR vsCATIDSolution = (char*) "{52AEFF70-BBD8-11d2-8598-006097C68E81}";
	const LPSTR vsCATIDSolutionBrowseObject = (char*) "{********-7C22-11d3-BDCA-00C04F688E50}";
	const LPSTR vsCATIDMiscFilesProject = (char*) "{610d4612-d0d5-11d2-8599-006097c68e81}";
	const LPSTR vsCATIDMiscFilesProjectItem = (char*) "{610d4613-d0d5-11d2-8599-006097c68e81}";
	const LPSTR vsCATIDGenericProject = (char*) "{610d4616-d0d5-11d2-8599-006097c68e81}";
	const LPSTR vsCATIDDocument = (char*) "{610d4611-d0d5-11d2-8599-006097c68e81}";

	enum __declspec(uuid("a457303f-d058-4415-a2b4-a81b148c7689"))
		vsFindOptions
	{
		vsFindOptionsNone = 0,
		vsFindOptionsMatchWholeWord = 2,
		vsFindOptionsMatchCase = 4,
		vsFindOptionsRegularExpression = 8,
		vsFindOptionsBackwards = 128,
		vsFindOptionsFromStart = 256,
		vsFindOptionsMatchInHiddenText = 512,
		vsFindOptionsWildcards = 1024,
		vsFindOptionsSearchSubfolders = 4096,
		vsFindOptionsKeepModifiedDocumentsOpen = 8192
	};

	enum __declspec(uuid("29182bd4-48a8-4e4b-81dd-1b742a3a5203"))
		vsMovementOptions
	{
		vsMovementOptionsMove = 0,
		vsMovementOptionsExtend = 1
	};

	enum __declspec(uuid("11b63a24-6200-48cd-9b8d-fc6a6fb9eff0"))
		vsGoToLineOptions
	{
		vsGoToLineOptionsLast = -1,
		vsGoToLineOptionsFirst = -2
	};

	enum __declspec(uuid("c512dde3-a220-4373-8950-e6636f8cc546"))
		vsSmartFormatOptions
	{
		vsSmartFormatOptionsNone = 0,
		vsSmartFormatOptionsBlock = 1,
		vsSmartFormatOptionsSmart = 2
	};

	enum __declspec(uuid("79515b53-8294-4f96-aa96-dd16bf126703"))
		vsInsertFlags
	{
		vsInsertFlagsCollapseToEnd = 1,
		vsInsertFlagsCollapseToStart = 2,
		vsInsertFlagsContainNewText = 4,
		vsInsertFlagsInsertAtEnd = 8,
		vsInsertFlagsInsertAtStart = 16
	};

	enum __declspec(uuid("4eb2c04f-8a83-4363-81db-dbb76cd76812"))
		vsMoveToColumnLine
	{
		vsMoveToColumnLineFirst = 0,
		vsMoveToColumnLineLast = 1
	};

	enum __declspec(uuid("3740dcf8-b8b2-49a9-a1ab-944c84876c3c"))
		vsEPReplaceTextOptions
	{
		vsEPReplaceTextKeepMarkers = 1,
		vsEPReplaceTextNormalizeNewlines = 2,
		vsEPReplaceTextTabsSpaces = 4,
		vsEPReplaceTextAutoformat = 8
	};

	struct __declspec(uuid("2e1bfd1c-5b26-4aca-b97b-ed9d261ba3e7"))
		IVsTextEditGeneral : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_SelectionMargin(
			/*[in]*/ VARIANT_BOOL pfSelectionMargin) = 0;
		virtual HRESULT __stdcall get_SelectionMargin(
			/*[out,retval]*/ VARIANT_BOOL * pfSelectionMargin) = 0;
		virtual HRESULT __stdcall put_GoToAnchorAfterEscape(
			/*[in]*/ VARIANT_BOOL pfGoToAnchorAfterEscape) = 0;
		virtual HRESULT __stdcall get_GoToAnchorAfterEscape(
			/*[out,retval]*/ VARIANT_BOOL * pfGoToAnchorAfterEscape) = 0;
		virtual HRESULT __stdcall put_DragNDropTextEditing(
			/*[in]*/ VARIANT_BOOL pfDragNDropTextEditing) = 0;
		virtual HRESULT __stdcall get_DragNDropTextEditing(
			/*[out,retval]*/ VARIANT_BOOL * pfDragNDropTextEditing) = 0;
		virtual HRESULT __stdcall put_UndoCaretActions(
			/*[in]*/ VARIANT_BOOL pfUndoCaretActions) = 0;
		virtual HRESULT __stdcall get_UndoCaretActions(
			/*[out,retval]*/ VARIANT_BOOL * pfUndoCaretActions) = 0;
		virtual HRESULT __stdcall put_MarginIndicatorBar(
			/*[in]*/ VARIANT_BOOL pfMarginIndicatorBar) = 0;
		virtual HRESULT __stdcall get_MarginIndicatorBar(
			/*[out,retval]*/ VARIANT_BOOL * pfMarginIndicatorBar) = 0;
		virtual HRESULT __stdcall put_HorizontalScrollBar(
			/*[in]*/ VARIANT_BOOL pfHorizontalScrollBar) = 0;
		virtual HRESULT __stdcall get_HorizontalScrollBar(
			/*[out,retval]*/ VARIANT_BOOL * pfHorizontalScrollBar) = 0;
		virtual HRESULT __stdcall put_VerticalScrollBar(
			/*[in]*/ VARIANT_BOOL pfVerticalScrollBar) = 0;
		virtual HRESULT __stdcall get_VerticalScrollBar(
			/*[out,retval]*/ VARIANT_BOOL * pfVerticalScrollBar) = 0;
		virtual HRESULT __stdcall put_AutoDelimiterHighlighting(
			/*[in]*/ VARIANT_BOOL pfHighlighting) = 0;
		virtual HRESULT __stdcall get_AutoDelimiterHighlighting(
			/*[out,retval]*/ VARIANT_BOOL * pfHighlighting) = 0;
	};

	struct __declspec(uuid("5943bd7e-d722-42db-a251-fe2add8711ea"))
		IVsTextEditPerLanguage : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_TabSize(
			/*[in]*/ short piTabSize) = 0;
		virtual HRESULT __stdcall get_TabSize(
			/*[out,retval]*/ short * piTabSize) = 0;
		virtual HRESULT __stdcall put_IndentSize(
			/*[in]*/ short piIndentSize) = 0;
		virtual HRESULT __stdcall get_IndentSize(
			/*[out,retval]*/ short * piIndentSize) = 0;
		virtual HRESULT __stdcall put_InsertTabs(
			/*[in]*/ VARIANT_BOOL pfInsertTabs) = 0;
		virtual HRESULT __stdcall get_InsertTabs(
			/*[out,retval]*/ VARIANT_BOOL * pfInsertTabs) = 0;
		virtual HRESULT __stdcall put_IndentStyle(
			/*[in]*/ vsIndentStyle pfIndentStyle) = 0;
		virtual HRESULT __stdcall get_IndentStyle(
			/*[out,retval]*/ vsIndentStyle * pfIndentStyle) = 0;
		virtual HRESULT __stdcall put_AutoListMembers(
			/*[in]*/ VARIANT_BOOL pfAutoListMembers) = 0;
		virtual HRESULT __stdcall get_AutoListMembers(
			/*[out,retval]*/ VARIANT_BOOL * pfAutoListMembers) = 0;
		virtual HRESULT __stdcall put_AutoListParams(
			/*[in]*/ VARIANT_BOOL pfAutoListParams) = 0;
		virtual HRESULT __stdcall get_AutoListParams(
			/*[out,retval]*/ VARIANT_BOOL * pfAutoListParams) = 0;
		virtual HRESULT __stdcall put_VirtualSpace(
			/*[in]*/ VARIANT_BOOL pfVirtualSpace) = 0;
		virtual HRESULT __stdcall get_VirtualSpace(
			/*[out,retval]*/ VARIANT_BOOL * pfVirtualSpace) = 0;
		virtual HRESULT __stdcall put_EnableLeftClickForURLs(
			/*[in]*/ VARIANT_BOOL pfHotURLs) = 0;
		virtual HRESULT __stdcall get_EnableLeftClickForURLs(
			/*[out,retval]*/ VARIANT_BOOL * pfHotURLs) = 0;
		virtual HRESULT __stdcall put_WordWrap(
			/*[in]*/ VARIANT_BOOL pfWrap) = 0;
		virtual HRESULT __stdcall get_WordWrap(
			/*[out,retval]*/ VARIANT_BOOL * pfWrap) = 0;
		virtual HRESULT __stdcall put_ShowLineNumbers(
			/*[in]*/ VARIANT_BOOL pfshow) = 0;
		virtual HRESULT __stdcall get_ShowLineNumbers(
			/*[out,retval]*/ VARIANT_BOOL * pfshow) = 0;
		virtual HRESULT __stdcall put_ShowNavigationBar(
			/*[in]*/ VARIANT_BOOL pfshow) = 0;
		virtual HRESULT __stdcall get_ShowNavigationBar(
			/*[out,retval]*/ VARIANT_BOOL * pfshow) = 0;
		virtual HRESULT __stdcall put_HideAdvancedMembers(
			/*[in]*/ VARIANT_BOOL pfHide) = 0;
		virtual HRESULT __stdcall get_HideAdvancedMembers(
			/*[out,retval]*/ VARIANT_BOOL * pfHide) = 0;
	};

	enum _vsIndentStyle
	{
		vsIndentStyleNone = 0,
		vsIndentStyleDefault = 1,
		vsIndentStyleSmart = 2
	};

	struct __declspec(uuid("f39ab913-e6c9-4546-a265-1e43f8de924c"))
		IVsTextEditFonts : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall put_FontFamily(
			/*[in]*/ BSTR pbstrFontFamily) = 0;
		virtual HRESULT __stdcall get_FontFamily(
			/*[out,retval]*/ BSTR * pbstrFontFamily) = 0;
		virtual HRESULT __stdcall put_FontCharacterSet(
			/*[in]*/ enum vsFontCharSet pbstrCharacterSet) = 0;
		virtual HRESULT __stdcall get_FontCharacterSet(
			/*[out,retval]*/ enum vsFontCharSet * pbstrCharacterSet) = 0;
		virtual HRESULT __stdcall put_FontSize(
			/*[in]*/ short piFontSize) = 0;
		virtual HRESULT __stdcall get_FontSize(
			/*[out,retval]*/ short * piFontSize) = 0;
	};

	enum __declspec(uuid("c106c8dc-6d0c-11d1-b2c9-00c04fb6c6ff"))
		vsext_FontCharSet
	{
		vsext_fontcs_ANSI = 0,
		vsext_fontcs_DEFAULT = 1,
		vsext_fontcs_SYMBOL = 2,
		vsext_fontcs_SHIFTJIS = 128,
		vsext_fontcs_HANGEUL = 129,
		vsext_fontcs_GB2312 = 134,
		vsext_fontcs_CHINESEBIG5 = 136,
		vsext_fontcs_OEM = 255,
		vsext_fontcs_JOHAB = 130,
		vsext_fontcs_HEBREW = 177,
		vsext_fontcs_ARABIC = 178,
		vsext_fontcs_GREEK = 161,
		vsext_fontcs_TURKISH = 162,
		vsext_fontcs_VIETNAMESE = 163,
		vsext_fontcs_THAI = 222,
		vsext_fontcs_EASTEUROPE = 238,
		vsext_fontcs_RUSSIAN = 204,
		vsext_fontcs_MAC = 77,
		vsext_fontcs_BALTIC = 186
	};

	enum VSEXECRESULT
	{
		RESULT_Success = -1,
		RESULT_Failure = 0,
		RESULT_Cancel = 1
	};

	enum __declspec(uuid("91aab9c3-da83-473a-8bc9-1a2730865995"))
		vsext_DisplayMode
	{
		vsext_dm_SDI = 0,
		vsext_dm_MDI = 1
	};

	enum __declspec(uuid("e4b9e990-fd59-11d0-bbc6-00a0c90f2744"))
		vsext_WindowType
	{
		vsext_wt_CodeWindow = 0,
		vsext_wt_Designer = 1,
		vsext_wt_Browser = 2,
		vsext_wt_Watch = 3,
		vsext_wt_Locals = 4,
		vsext_wt_Immediate = 5,
		vsext_wt_ProjectWindow = 6,
		vsext_wt_PropertyWindow = 7,
		vsext_wt_Find = 8,
		vsext_wt_FindReplace = 9,
		vsext_wt_Toolbox = 10,
		vsext_wt_LinkedWindowFrame = 11,
		vsext_wt_MainWindow = 12,
		vsext_wt_Preview = 13,
		vsext_wt_ColorPalette = 14,
		vsext_wt_ToolWindow = 15,
		vsext_wt_Document = 16,
		vsext_wt_OutPutWindow = 17,
		vsext_wt_TaskList = 18,
		vsext_wt_Autos = 19,
		vsext_wt_CallStack = 20,
		vsext_wt_Threads = 21,
		vsext_wt_DocumentOutline = 22,
		vsext_wt_RunningDocuments = 23
	};

	enum __declspec(uuid("e0c72e51-6b99-489b-aca6-90cedce888e7"))
		vsext_WindowState
	{
		vsext_ws_Normal = 0,
		vsext_ws_Minimize = 1,
		vsext_ws_Maximize = 2
	};

	enum __declspec(uuid("97653acf-d869-4ec0-b450-e9842457c729"))
		vsext_LinkedWindowType
	{
		vsext_lwt_Docked = 0,
		vsext_lwt_Tabbed = 1
	};

	enum __declspec(uuid("a5578594-18e0-11d1-bbdb-00a0c90f2744"))
		vsext_StartUp
	{
		vsext_su_EMPTY_ENVIRONMENT = 0,
		vsext_su_NEW_SOLUTION_DIALOG = 1,
		vsext_su_LOAD_LAST_SOLUTION = 2
	};

	enum __declspec(uuid("47046891-19f7-11d1-bcaf-000000000000"))
		vsext_Build
	{
		vsext_bld_SAVE_CHANGES = 0,
		vsext_bld_CONFIRM_SAVE = 1,
		vsext_bld_NO_SAVE = 2
	};

	enum __declspec(uuid("bdbe10aa-74d3-498a-a464-8074cd5e99cb"))
		DsTextSearchOptions
	{
		dsMatchWord = 2,
		dsMatchCase = 4,
		dsMatchNoRegExp = 0,
		dsMatchRegExp = 8,
		dsMatchRegExpB = 16,
		dsMatchRegExpE = 32,
		dsMatchRegExpCur = 64,
		dsMatchForward = 0,
		dsMatchBackward = 128,
		dsMatchFromStart = 256
	};

	enum __declspec(uuid("99744ec4-7a72-11d2-adc9-00c04f7971c3"))
		DsSaveChanges
	{
		dsSaveChangesYes = 1,
		dsSaveChangesNo = 2,
		dsSaveChangesPrompt = 3
	};

	enum __declspec(uuid("6269c78d-5591-40d6-a095-5bac8ba07478"))
		DsGoToLineOptions
	{
		dsLastLine = -1
	};

	enum __declspec(uuid("95751137-ac07-4c76-a219-4be5d381985d"))
		DsStartOfLineOptions
	{
		dsFirstColumn = 0,
		dsFirstText = 1
	};

	enum __declspec(uuid("4b58f807-6f08-4b41-abaf-b5bca4869d2c"))
		DsMovementOptions
	{
		dsMove = 0,
		dsExtend = 1
	};

	enum __declspec(uuid("31445615-bcae-4ba0-a99c-246bc660cfe8"))
		DsWhitespaceOptions
	{
		dsHorizontal = 0,
		dsVertical = 1
	};

	enum __declspec(uuid("f7d38518-e803-4462-8fa2-e481642d42fb"))
		DsCaseOptions
	{
		dsLowercase = 1,
		dsUppercase = 2,
		dsCapitalize = 3
	};

	enum __declspec(uuid("8d78b9a9-a3be-49d0-aa97-bf7fa46d8ea2"))
		dsSaveStatus
	{
		dsSaveCancelled = 2,
		dsSaveSucceeded = 1
	};

	enum __declspec(uuid("44160df5-e305-4d85-b7fe-6b5a1b81b9b0"))
		vsHTMLTabs
	{
		vsHTMLTabsSource = 0,
		vsHTMLTabsDesign = 1
	};

	struct __declspec(uuid("04a72314-32e9-48e2-9b87-a63603454f3e"))
		_DTE : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_FileName(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_Version(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_CommandBars(
			/*[out,retval]*/ IDispatch * * ppcbs) = 0;
		virtual HRESULT __stdcall get_Windows(
			/*[out,retval]*/ struct Windows * * ppwnsVBWindows) = 0;
		virtual HRESULT __stdcall get_Events(
			/*[out,retval]*/ struct Events * * ppevtEvents) = 0;
		virtual HRESULT __stdcall get_AddIns(
			/*[out,retval]*/ struct AddIns * * lpppAddIns) = 0;
		virtual HRESULT __stdcall get_MainWindow(
			/*[out,retval]*/ struct Window * * ppWin) = 0;
		virtual HRESULT __stdcall get_ActiveWindow(
			/*[out,retval]*/ struct Window * * ppwinActive) = 0;
		virtual HRESULT __stdcall Quit() = 0;
		virtual HRESULT __stdcall get_DisplayMode(
			/*[out,retval]*/ enum vsDisplay * lpDispMode) = 0;
		virtual HRESULT __stdcall put_DisplayMode(
			/*[in]*/ enum vsDisplay lpDispMode) = 0;
		virtual HRESULT __stdcall get_Solution(
			/*[out,retval]*/ struct _Solution * * ppSolution) = 0;
		virtual HRESULT __stdcall get_Commands(
			/*[out,retval]*/ struct Commands * * ppCommands) = 0;
		virtual HRESULT __stdcall GetObject(
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ IDispatch * * ppObject) = 0;
		virtual HRESULT __stdcall get_Properties(
			BSTR Category,
			BSTR Page,
			/*[out,retval]*/ struct Properties * * ppObject) = 0;
		virtual HRESULT __stdcall get_SelectedItems(
			/*[out,retval]*/ struct SelectedItems * * ppSelectedItems) = 0;
		virtual HRESULT __stdcall get_CommandLineArguments(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall OpenFile(
			/*[in]*/ BSTR ViewKind,
			/*[in]*/ BSTR FileName,
			/*[out,retval]*/ struct Window * * ppWin) = 0;
		virtual HRESULT __stdcall get_IsOpenFile(
			/*[in]*/ BSTR ViewKind,
			/*[in]*/ BSTR FileName,
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_LocaleID(
			/*[out,retval]*/ long * lpReturn) = 0;
		virtual HRESULT __stdcall get_WindowConfigurations(
			/*[out,retval]*/ struct WindowConfigurations * * WindowConfigurationsObject) = 0;
		virtual HRESULT __stdcall get_Documents(
			/*[out,retval]*/ struct Documents * * ppDocuments) = 0;
		virtual HRESULT __stdcall get_ActiveDocument(
			/*[out,retval]*/ struct Document * * ppDocument) = 0;
		virtual HRESULT __stdcall ExecuteCommand(
			/*[in]*/ BSTR CommandName,
			/*[in]*/ BSTR CommandArgs) = 0;
		virtual HRESULT __stdcall get_Globals(
			/*[out,retval]*/ struct Globals * * ppGlobals) = 0;
		virtual HRESULT __stdcall get_StatusBar(
			/*[out,retval]*/ struct StatusBar * * ppStatusBar) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_UserControl(
			/*[out,retval]*/ VARIANT_BOOL * UserControl) = 0;
		virtual HRESULT __stdcall put_UserControl(
			/*[in]*/ VARIANT_BOOL UserControl) = 0;
		virtual HRESULT __stdcall get_ObjectExtenders(
			/*[out,retval]*/ struct ObjectExtenders * * ppObjectExtenders) = 0;
		virtual HRESULT __stdcall get_Find(
			/*[out,retval]*/ struct Find * * ppFind) = 0;
		virtual HRESULT __stdcall get_Mode(
			/*[out,retval]*/ enum vsIDEMode * pVal) = 0;
		virtual HRESULT __stdcall LaunchWizard(
			/*[in]*/ BSTR VSZFile,
			/*[in]*/ SAFEARRAY * * ContextParams,
			/*[out,retval]*/ enum wizardResult * pResult) = 0;
		virtual HRESULT __stdcall get_ItemOperations(
			/*[out,retval]*/ struct ItemOperations * * ppItemOperations) = 0;
		virtual HRESULT __stdcall get_UndoContext(
			/*[out,retval]*/ struct UndoContext * * ppUndoContext) = 0;
		virtual HRESULT __stdcall get_Macros(
			/*[out,retval]*/ struct Macros * * ppMacros) = 0;
		virtual HRESULT __stdcall get_ActiveSolutionProjects(
			/*[out,retval]*/ VARIANT * pProjects) = 0;
		virtual HRESULT __stdcall get_MacrosIDE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_RegistryRoot(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_Application(
			/*[out,retval]*/ struct _DTE * * pVal) = 0;
		virtual HRESULT __stdcall get_ContextAttributes(
			/*[out,retval]*/ struct ContextAttributes * * ppVal) = 0;
		virtual HRESULT __stdcall get_SourceControl(
			/*[out,retval]*/ struct SourceControl * * ppVal) = 0;
		virtual HRESULT __stdcall get_SuppressUI(
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall put_SuppressUI(
			/*[in]*/ VARIANT_BOOL pVal) = 0;
		virtual HRESULT __stdcall get_Debugger(
			/*[out,retval]*/ struct Debugger * * ppDebugger) = 0;
		virtual HRESULT __stdcall SatelliteDllPath(
			BSTR Path,
			BSTR Name,
			/*[out,retval]*/ BSTR * pFullPath) = 0;
		virtual HRESULT __stdcall get_Edition(
			/*[out,retval]*/ BSTR * ProductEdition) = 0;
	};

	struct __declspec(uuid("2294311a-b7bc-4789-b365-1c15ff2cd17c"))
		Windows : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Window * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall CreateToolWindow(
			/*[in]*/ struct AddIn * AddInInst,
			/*[in]*/ BSTR ProgID,
			/*[in]*/ BSTR Caption,
			/*[in]*/ BSTR GuidPosition,
			/*[in,out]*/ IDispatch * * DocObj,
			/*[out,retval]*/ struct Window * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall CreateLinkedWindowFrame(
			/*[in]*/ struct Window * Window1,
			/*[in]*/ struct Window * Window2,
			/*[in]*/ enum vsLinkedWindowType Link,
			/*[out,retval]*/ struct Window * * LinkedWindowFrame) = 0;
	};

	struct __declspec(uuid("0beab46b-4c07-4f94-a8d7-1626020e4e53"))
		Window : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Windows * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Visible(
			/*[out,retval]*/ VARIANT_BOOL * pfVisible) = 0;
		virtual HRESULT __stdcall put_Visible(
			/*[in]*/ VARIANT_BOOL pfVisible) = 0;
		virtual HRESULT __stdcall get_Left(
			/*[out,retval]*/ long * plLeft) = 0;
		virtual HRESULT __stdcall put_Left(
			/*[in]*/ long plLeft) = 0;
		virtual HRESULT __stdcall get_Top(
			/*[out,retval]*/ long * plTop) = 0;
		virtual HRESULT __stdcall put_Top(
			/*[in]*/ long plTop) = 0;
		virtual HRESULT __stdcall get_Width(
			/*[out,retval]*/ long * plWidth) = 0;
		virtual HRESULT __stdcall put_Width(
			/*[in]*/ long plWidth) = 0;
		virtual HRESULT __stdcall get_Height(
			/*[out,retval]*/ long * plHeight) = 0;
		virtual HRESULT __stdcall put_Height(
			/*[in]*/ long plHeight) = 0;
		virtual HRESULT __stdcall get_WindowState(
			/*[out,retval]*/ enum vsWindowState * plWindowState) = 0;
		virtual HRESULT __stdcall put_WindowState(
			/*[in]*/ enum vsWindowState plWindowState) = 0;
		virtual HRESULT __stdcall SetFocus() = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ enum vsWindowType * pKind) = 0;
		virtual HRESULT __stdcall SetKind(
			/*[in]*/ enum vsWindowType eKind) = 0;
		virtual HRESULT __stdcall get_LinkedWindows(
			/*[out,retval]*/ struct LinkedWindows * * ppwnsCollection) = 0;
		virtual HRESULT __stdcall get_LinkedWindowFrame(
			/*[out,retval]*/ struct Window * * ppwinFrame) = 0;
		virtual HRESULT __stdcall Detach() = 0;
		virtual HRESULT __stdcall Attach(
			/*[in]*/ long lWindowHandle) = 0;
		virtual HRESULT __stdcall get_HWnd(
			/*[out,retval]*/ long * plWindowHandle) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ BSTR * pbstrType) = 0;
		virtual HRESULT __stdcall get_ObjectKind(
			/*[out,retval]*/ BSTR * pbstrTypeGUID) = 0;
		virtual HRESULT __stdcall get_Object(
			/*[out,retval]*/ IDispatch * * ppToolObject) = 0;
		virtual HRESULT __stdcall get_DocumentData(
			/*[in]*/ BSTR bstrWhichData,
			/*[out,retval]*/ IDispatch * * ppDataObject) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * ppProjItem) = 0;
		virtual HRESULT __stdcall get_Project(
			/*[out,retval]*/ struct Project * * ppProj) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Document(
			/*[out,retval]*/ struct Document * * ppDocument) = 0;
		virtual HRESULT __stdcall get_Selection(
			/*[out,retval]*/ IDispatch * * ppDocument) = 0;
		virtual HRESULT __stdcall get_Linkable(
			/*[out,retval]*/ VARIANT_BOOL * pLinkable) = 0;
		virtual HRESULT __stdcall put_Linkable(
			/*[in]*/ VARIANT_BOOL pLinkable) = 0;
		virtual HRESULT __stdcall Activate() = 0;
		virtual HRESULT __stdcall Close(
			/*[in]*/ enum vsSaveChanges SaveChanges) = 0;
		virtual HRESULT __stdcall get_Caption(
			/*[out,retval]*/ BSTR * pbstrTitle) = 0;
		virtual HRESULT __stdcall put_Caption(
			/*[in]*/ BSTR pbstrTitle) = 0;
		virtual HRESULT __stdcall SetSelectionContainer(
			SAFEARRAY * * Objects) = 0;
		virtual HRESULT __stdcall get_IsFloating(
			/*[out,retval]*/ VARIANT_BOOL * Floating) = 0;
		virtual HRESULT __stdcall put_IsFloating(
			/*[in]*/ VARIANT_BOOL Floating) = 0;
		virtual HRESULT __stdcall get_AutoHides(
			/*[out,retval]*/ VARIANT_BOOL * Hides) = 0;
		virtual HRESULT __stdcall put_AutoHides(
			/*[in]*/ VARIANT_BOOL Hides) = 0;
		virtual HRESULT __stdcall SetTabPicture(
			VARIANT Picture) = 0;
		virtual HRESULT __stdcall get_ContextAttributes(
			/*[out,retval]*/ struct ContextAttributes * * ppVal) = 0;
	};

	struct __declspec(uuid("f00ef34a-a654-4c1b-897a-585d5bcbb35a"))
		LinkedWindows : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * ppptReturn) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Window * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall Remove(
			/*[in]*/ struct Window * Window) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ struct Window * Window) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
	};

	struct __declspec(uuid("f47dc7e7-84b6-474f-bb91-631640aa0560"))
		TextBuffer : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * retval) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall AddFromString(
			/*[in]*/ BSTR String,
			/*[in]*/ long StartLine) = 0;
		virtual HRESULT __stdcall AddFromFile(
			/*[in]*/ BSTR FileName,
			/*[in]*/ long StartLine) = 0;
		virtual HRESULT __stdcall get_Lines(
			/*[in]*/ long StartLine,
			/*[in]*/ long Count,
			/*[out,retval]*/ BSTR * String) = 0;
		virtual HRESULT __stdcall get_CountOfLines(
			/*[out,retval]*/ long * CountOfLines) = 0;
		virtual HRESULT __stdcall DeleteLines(
			/*[in]*/ long StartLine,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall Find(
			/*[in]*/ BSTR Target,
			/*[in,out]*/ long * StartLine,
			/*[in,out]*/ long * StartColumn,
			/*[in,out]*/ long * EndLine,
			/*[in,out]*/ long * EndColumn,
			/*[in]*/ VARIANT_BOOL WholeWord,
			/*[in]*/ VARIANT_BOOL MatchCase,
			/*[in]*/ VARIANT_BOOL PatternSearch,
			/*[out,retval]*/ VARIANT_BOOL * pfFound) = 0;
	};

	struct __declspec(uuid("17d12026-ba99-403e-a359-71fd1e5a72cd"))
		_WindowEventsRoot : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_WindowEvents(
			/*[in]*/ struct Window * WindowFilter,
			/*[out,retval]*/ IDispatch * * ppDisp) = 0;
	};

	struct __declspec(uuid("f6576203-fbce-477e-a66b-eda237bb68a7"))
		HTMLWindow : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * ppParent) = 0;
		virtual HRESULT __stdcall get_CurrentTab(
			/*[out,retval]*/ enum vsHTMLTabs * pTab) = 0;
		virtual HRESULT __stdcall put_CurrentTab(
			/*[in]*/ enum vsHTMLTabs pTab) = 0;
		virtual HRESULT __stdcall get_CurrentTabObject(
			/*[out,retval]*/ IDispatch * * ppObj) = 0;
	};

	struct __declspec(uuid("0b48100a-473e-433c-ab8f-66b9739ab620"))
		ProjectItem : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_IsDirty(
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall put_IsDirty(
			/*[in]*/ VARIANT_BOOL lpfReturn) = 0;
		virtual HRESULT __stdcall get_FileNames(
			/*[in]*/ short index,
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall SaveAs(
			/*[in]*/ BSTR NewFileName,
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall get_FileCount(
			/*[out,retval]*/ short * lpsReturn) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pbstrReturn) = 0;
		virtual HRESULT __stdcall put_Name(
			/*[in]*/ BSTR pbstrReturn) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct ProjectItems * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_Properties(
			/*[out,retval]*/ struct Properties * * ppObject) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ BSTR * lpbstrFileName) = 0;
		virtual HRESULT __stdcall get_ProjectItems(
			/*[out,retval]*/ struct ProjectItems * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_IsOpen(
			/*[in]*/ BSTR ViewKind,
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall Open(
			/*[in]*/ BSTR ViewKind,
			/*[out,retval]*/ struct Window * * lppfReturn) = 0;
		virtual HRESULT __stdcall Remove() = 0;
		virtual HRESULT __stdcall ExpandView() = 0;
		virtual HRESULT __stdcall get_Object(
			/*[out,retval]*/ IDispatch * * ProjectItemModel) = 0;
		virtual HRESULT __stdcall get_Extender(
			/*[in]*/ BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall get_Saved(
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall put_Saved(
			/*[in]*/ VARIANT_BOOL lpfReturn) = 0;
		virtual HRESULT __stdcall get_ConfigurationManager(
			/*[out,retval]*/ struct ConfigurationManager * * ppConfigurationManager) = 0;
		virtual HRESULT __stdcall get_FileCodeModel(
			/*[out,retval]*/ struct FileCodeModel * * ppFileCodeModel) = 0;
		virtual HRESULT __stdcall Save(
			BSTR FileName) = 0;
		virtual HRESULT __stdcall get_Document(
			/*[out,retval]*/ struct Document * * ppDocument) = 0;
		virtual HRESULT __stdcall get_SubProject(
			/*[out,retval]*/ struct Project * * ppProject) = 0;
		virtual HRESULT __stdcall get_ContainingProject(
			/*[out,retval]*/ struct Project * * ppProject) = 0;
		virtual HRESULT __stdcall Delete() = 0;
	};

	struct __declspec(uuid("d5dbe57b-c074-4e95-b015-abeeaa391693"))
		ItemOperations : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall OpenFile(
			/*[in]*/ BSTR FileName,
			/*[in]*/ BSTR ViewKind,
			/*[out,retval]*/ struct Window * * Window) = 0;
		virtual HRESULT __stdcall NewFile(
			/*[in]*/ BSTR Item,
			/*[in]*/ BSTR Name,
			/*[in]*/ BSTR ViewKind,
			/*[out,retval]*/ struct Window * * Window) = 0;
		virtual HRESULT __stdcall IsFileOpen(
			/*[in]*/ BSTR FileName,
			/*[in]*/ BSTR ViewKind,
			/*[out,retval]*/ VARIANT_BOOL * pfRetval) = 0;
		virtual HRESULT __stdcall AddExistingItem(
			/*[in]*/ BSTR FileName,
			/*[out,retval]*/ struct ProjectItem * * ProjectItem) = 0;
		virtual HRESULT __stdcall AddNewItem(
			/*[in]*/ BSTR Item,
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ struct ProjectItem * * ProjectItem) = 0;
		virtual HRESULT __stdcall Navigate(
			/*[in]*/ BSTR URL,
			/*[in]*/ enum vsNavigateOptions Options,
			/*[out,retval]*/ struct Window * * Window) = 0;
		virtual HRESULT __stdcall get_PromptToSave(
			/*[out,retval]*/ enum vsPromptResult * Saved) = 0;
	};

	struct __declspec(uuid("8e2f1269-185e-43c7-8899-950ad2769ccf"))
		ProjectItems : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct ProjectItem * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * lppptReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ BSTR * lpbstrFileName) = 0;
		virtual HRESULT __stdcall AddFromFile(
			/*[in]*/ BSTR FileName,
			/*[out,retval]*/ struct ProjectItem * * lppcReturn) = 0;
		virtual HRESULT __stdcall AddFromTemplate(
			/*[in]*/ BSTR FileName,
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ struct ProjectItem * * lppcReturn) = 0;
		virtual HRESULT __stdcall AddFromDirectory(
			/*[in]*/ BSTR Directory,
			/*[out,retval]*/ struct ProjectItem * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_ContainingProject(
			/*[out,retval]*/ struct Project * * ppProject) = 0;
		virtual HRESULT __stdcall AddFolder(
			BSTR Name,
			BSTR Kind,
			/*[out,retval]*/ struct ProjectItem * * pProjectItem) = 0;
		virtual HRESULT __stdcall AddFromFileCopy(
			BSTR FilePath,
			/*[out,retval]*/ struct ProjectItem * * pProjectItem) = 0;
	};

	struct __declspec(uuid("866311e6-c887-4143-9833-645f5b93f6f1"))
		Project : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * lpbstrName) = 0;
		virtual HRESULT __stdcall put_Name(
			/*[in]*/ BSTR lpbstrName) = 0;
		virtual HRESULT __stdcall get_FileName(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_IsDirty(
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall put_IsDirty(
			/*[in]*/ VARIANT_BOOL lpfReturn) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Projects * * lppaReturn) = 0;
		virtual HRESULT __stdcall SaveAs(
			/*[in]*/ BSTR NewFileName) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ BSTR * lpbstrFileName) = 0;
		virtual HRESULT __stdcall get_ProjectItems(
			/*[out,retval]*/ struct ProjectItems * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_Properties(
			/*[out,retval]*/ struct Properties * * ppObject) = 0;
		virtual HRESULT __stdcall get_UniqueName(
			/*[out,retval]*/ BSTR * lpbstrFileName) = 0;
		virtual HRESULT __stdcall get_Object(
			/*[out,retval]*/ IDispatch * * ProjectModel) = 0;
		virtual HRESULT __stdcall get_Extender(
			/*[in]*/ BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_Saved(
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall put_Saved(
			/*[in]*/ VARIANT_BOOL lpfReturn) = 0;
		virtual HRESULT __stdcall get_ConfigurationManager(
			/*[out,retval]*/ struct ConfigurationManager * * ppConfigurationManager) = 0;
		virtual HRESULT __stdcall get_Globals(
			/*[out,retval]*/ struct Globals * * ppGlobals) = 0;
		virtual HRESULT __stdcall Save(
			BSTR FileName) = 0;
		virtual HRESULT __stdcall get_ParentProjectItem(
			/*[out,retval]*/ struct ProjectItem * * ppParentProjectItem) = 0;
		virtual HRESULT __stdcall get_CodeModel(
			/*[out,retval]*/ struct CodeModel * * ppCodeModel) = 0;
		virtual HRESULT __stdcall Delete() = 0;
	};

	struct __declspec(uuid("e3ec0add-31b3-461f-8303-8a5e6931257a"))
		Projects : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Project * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Properties(
			/*[out,retval]*/ struct Properties * * ppObject) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
	};

	struct __declspec(uuid("4cc8ccf5-a926-4646-b17f-b4940caed472"))
		Properties : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Property * * lplppReturn) = 0;
		virtual HRESULT __stdcall get_Application(
			/*[out,retval]*/ IDispatch * * lppidReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * lppidReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
	};

	struct __declspec(uuid("7b988e06-2581-485e-9322-04881e0600d0"))
		Property : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Value(
			/*[out,retval]*/ VARIANT * lppvReturn) = 0;
		virtual HRESULT __stdcall put_Value(
			/*[in]*/ VARIANT lppvReturn) = 0;
		virtual HRESULT __stdcall putref_Value(
			/*[in]*/ VARIANT lppvReturn) = 0;
		virtual HRESULT __stdcall get_IndexedValue(
			/*[in]*/ VARIANT Index1,
			/*[in]*/ VARIANT Index2,
			/*[in]*/ VARIANT Index3,
			/*[in]*/ VARIANT Index4,
			/*[out,retval]*/ VARIANT * Val) = 0;
		virtual HRESULT __stdcall put_IndexedValue(
			/*[in]*/ VARIANT Index1,
			/*[in]*/ VARIANT Index2,
			/*[in]*/ VARIANT Index3,
			/*[in]*/ VARIANT Index4,
			/*[in]*/ VARIANT Val) = 0;
		virtual HRESULT __stdcall get_NumIndices(
			/*[out,retval]*/ short * lpiRetVal) = 0;
		virtual HRESULT __stdcall get_Application(
			/*[out,retval]*/ IDispatch * * lppidReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Properties * * lpppReturn) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Properties * * lpppReturn) = 0;
		virtual HRESULT __stdcall get_Object(
			/*[out,retval]*/ IDispatch * * lppunk) = 0;
		virtual HRESULT __stdcall putref_Object(
			/*[in]*/ IUnknown * lppunk) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
	};

	struct __declspec(uuid("9043fda1-345b-4364-900f-bc8598eb8e4f"))
		ConfigurationManager : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ppParent) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			BSTR Platform,
			/*[out,retval]*/ struct Configuration * * ppOut) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall ConfigurationRow(
			BSTR Name,
			/*[out,retval]*/ struct Configurations * * ppOut) = 0;
		virtual HRESULT __stdcall AddConfigurationRow(
			BSTR NewName,
			BSTR ExistingName,
			/*[in]*/ VARIANT_BOOL Propagate,
			/*[out,retval]*/ struct Configurations * * ppOut) = 0;
		virtual HRESULT __stdcall DeleteConfigurationRow(
			BSTR Name) = 0;
		virtual HRESULT __stdcall get_ConfigurationRowNames(
			/*[out,retval]*/ VARIANT * pNames) = 0;
		virtual HRESULT __stdcall Platform(
			BSTR Name,
			/*[out,retval]*/ struct Configurations * * ppOut) = 0;
		virtual HRESULT __stdcall AddPlatform(
			BSTR NewName,
			BSTR ExistingName,
			/*[in]*/ VARIANT_BOOL Propagate,
			/*[out,retval]*/ struct Configurations * * ppOut) = 0;
		virtual HRESULT __stdcall DeletePlatform(
			BSTR Name) = 0;
		virtual HRESULT __stdcall get_PlatformNames(
			/*[out,retval]*/ VARIANT * pNames) = 0;
		virtual HRESULT __stdcall get_SupportedPlatforms(
			/*[out,retval]*/ VARIANT * pPlatforms) = 0;
		virtual HRESULT __stdcall get_ActiveConfiguration(
			/*[out,retval]*/ struct Configuration * * ppOut) = 0;
	};

	struct __declspec(uuid("90813589-fe21-4aa4-a2e5-053fd274e980"))
		Configuration : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct ConfigurationManager * * ppConfigurationManager) = 0;
		virtual HRESULT __stdcall get_ConfigurationName(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_PlatformName(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ enum vsConfigurationType * pType) = 0;
		virtual HRESULT __stdcall get_Owner(
			/*[out,retval]*/ IDispatch * * ppOwner) = 0;
		virtual HRESULT __stdcall get_Properties(
			/*[out,retval]*/ struct Properties * * ppProperties) = 0;
		virtual HRESULT __stdcall get_IsBuildable(
			/*[out,retval]*/ VARIANT_BOOL * pBuildable) = 0;
		virtual HRESULT __stdcall get_IsRunable(
			/*[out,retval]*/ VARIANT_BOOL * pRunable) = 0;
		virtual HRESULT __stdcall get_IsDeployable(
			/*[out,retval]*/ VARIANT_BOOL * pDeployable) = 0;
		virtual HRESULT __stdcall get_Object(
			/*[out,retval]*/ IDispatch * * ppDisp) = 0;
		virtual HRESULT __stdcall get_Extender(
			/*[in]*/ BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall get_OutputGroups(
			/*[out,retval]*/ struct OutputGroups * * ppOutputGroups) = 0;
	};

	struct __declspec(uuid("b6b4c8d6-4d27-43b9-b45c-52bd16b6ba38"))
		Configurations : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ppParent) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Configuration * * ppOut) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ enum vsConfigurationType * pType) = 0;
	};

	struct __declspec(uuid("f9fa748e-e302-44cf-891b-e263189d585e"))
		OutputGroups : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Configuration * * ppConfiguration) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct OutputGroup * * ppOut) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
	};

	struct __declspec(uuid("a3a80783-875f-435b-9639-e5ce888df737"))
		OutputGroup : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct OutputGroups * * ppOutputGroups) = 0;
		virtual HRESULT __stdcall get_FileNames(
			/*[out,retval]*/ VARIANT * pNames) = 0;
		virtual HRESULT __stdcall get_FileCount(
			/*[out,retval]*/ long * pCountNames) = 0;
		virtual HRESULT __stdcall get_DisplayName(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_CanonicalName(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_FileURLs(
			/*[out,retval]*/ VARIANT * pURLs) = 0;
		virtual HRESULT __stdcall get_Description(
			/*[out,retval]*/ BSTR * pDesc) = 0;
	};

	struct __declspec(uuid("0cfbc2b4-0d4e-11d3-8997-00c04f688dde"))
		CodeModel : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Project * * pProj) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_CodeElements(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall CodeTypeFromFullName(
			BSTR Name,
			/*[out,retval]*/ struct CodeType * * ppCodeType) = 0;
		virtual HRESULT __stdcall AddNamespace(
			BSTR Name,
			VARIANT Location,
			VARIANT Position,
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall AddClass(
			BSTR Name,
			VARIANT Location,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeClass * * ppCodeClass) = 0;
		virtual HRESULT __stdcall AddInterface(
			BSTR Name,
			VARIANT Location,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeInterface * * ppCodeInterface) = 0;
		virtual HRESULT __stdcall AddFunction(
			BSTR Name,
			VARIANT Location,
			enum vsCMFunction Kind,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeFunction * * ppCodeFunction) = 0;
		virtual HRESULT __stdcall AddVariable(
			BSTR Name,
			VARIANT Location,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeVariable * * ppCodeVariable) = 0;
		virtual HRESULT __stdcall AddStruct(
			BSTR Name,
			VARIANT Location,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeStruct * * ppCodeStruct) = 0;
		virtual HRESULT __stdcall AddEnum(
			BSTR Name,
			VARIANT Location,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeEnum * * ppCodeEnum) = 0;
		virtual HRESULT __stdcall AddDelegate(
			BSTR Name,
			VARIANT Location,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeDelegate * * ppCodeDelegate) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			VARIANT Location,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall Remove(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall IsValidID(
			BSTR Name,
			/*[out,retval]*/ VARIANT_BOOL * pValid) = 0;
		virtual HRESULT __stdcall get_IsCaseSensitive(
			/*[out,retval]*/ VARIANT_BOOL * pSensitive) = 0;
		virtual HRESULT __stdcall CreateCodeTypeRef(
			VARIANT Type,
			/*[out,retval]*/ struct CodeTypeRef * * ppCodeTypeRef) = 0;
	};

	struct __declspec(uuid("0cfbc2b5-0d4e-11d3-8997-00c04f688dde"))
		CodeElements : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall Item(
			VARIANT index,
			/*[out,retval]*/ struct CodeElement * * ppCodeElement) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * CountOfCodeElements) = 0;
		virtual HRESULT __stdcall Reserved1(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall CreateUniqueID(
			/*[in]*/ BSTR Prefix,
			/*[in,out]*/ BSTR * NewName,
			/*[out,retval]*/ VARIANT_BOOL * pRootUnique) = 0;
	};

	struct __declspec(uuid("0cfbc2b6-0d4e-11d3-8997-00c04f688dde"))
		CodeElement : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
	};

	struct __declspec(uuid("7f59e94e-4939-40d2-9f7f-b7651c25905d"))
		TextPoint : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct TextDocument * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Line(
			/*[out,retval]*/ long * lppaReturn) = 0;
		virtual HRESULT __stdcall get_LineCharOffset(
			/*[out,retval]*/ long * lppaReturn) = 0;
		virtual HRESULT __stdcall get_AbsoluteCharOffset(
			/*[out,retval]*/ long * lppaReturn) = 0;
		virtual HRESULT __stdcall get_DisplayColumn(
			/*[out,retval]*/ long * lppaReturn) = 0;
		virtual HRESULT __stdcall get_AtEndOfDocument(
			/*[out,retval]*/ VARIANT_BOOL * lppaReturn) = 0;
		virtual HRESULT __stdcall get_AtStartOfDocument(
			/*[out,retval]*/ VARIANT_BOOL * lppaReturn) = 0;
		virtual HRESULT __stdcall get_AtEndOfLine(
			/*[out,retval]*/ VARIANT_BOOL * lppaReturn) = 0;
		virtual HRESULT __stdcall get_AtStartOfLine(
			/*[out,retval]*/ VARIANT_BOOL * lppaReturn) = 0;
		virtual HRESULT __stdcall get_LineLength(
			/*[out,retval]*/ long * lppaReturn) = 0;
		virtual HRESULT __stdcall EqualTo(
			/*[in]*/ struct TextPoint * Point,
			/*[out,retval]*/ VARIANT_BOOL * lppaReturn) = 0;
		virtual HRESULT __stdcall LessThan(
			/*[in]*/ struct TextPoint * Point,
			/*[out,retval]*/ VARIANT_BOOL * lppaReturn) = 0;
		virtual HRESULT __stdcall GreaterThan(
			/*[in]*/ struct TextPoint * Point,
			/*[out,retval]*/ VARIANT_BOOL * lppaReturn) = 0;
		virtual HRESULT __stdcall TryToShow(
			/*[in]*/ enum vsPaneShowHow How,
			/*[in]*/ VARIANT PointOrCount,
			/*[out,retval]*/ VARIANT_BOOL * pbResult) = 0;
		virtual HRESULT __stdcall get_CodeElement(
			/*[in]*/ enum vsCMElement Scope,
			/*[out,retval]*/ struct CodeElement * * ppResult) = 0;
		virtual HRESULT __stdcall CreateEditPoint(
			/*[out,retval]*/ struct EditPoint * * lppaReturn) = 0;
	};

	struct __declspec(uuid("42320454-626c-4dd0-9ecb-357c4f1966d8"))
		VirtualPoint : TextPoint
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_VirtualCharOffset(
			/*[out,retval]*/ long * pOffset) = 0;
		virtual HRESULT __stdcall get_VirtualDisplayColumn(
			/*[out,retval]*/ long * lppaReturn) = 0;
	};

	struct __declspec(uuid("0cfbc2be-0d4e-11d3-8997-00c04f688dde"))
		CodeAttribute : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ppParent) = 0;
		virtual HRESULT __stdcall get_Value(
			/*[out,retval]*/ BSTR * pValue) = 0;
		virtual HRESULT __stdcall put_Value(
			BSTR pValue) = 0;
		virtual HRESULT __stdcall Delete() = 0;
	};

	struct __declspec(uuid("cb218890-1382-472b-9118-782700c88115"))
		TextDocument : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Document * * ppParent) = 0;
		virtual HRESULT __stdcall get_Selection(
			/*[out,retval]*/ struct TextSelection * * ppSel) = 0;
		virtual HRESULT __stdcall ClearBookmarks() = 0;
		virtual HRESULT __stdcall MarkText(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ long vsFindOptionsValue,
			/*[out,retval]*/ VARIANT_BOOL * pbRetVal) = 0;
		virtual HRESULT __stdcall ReplacePattern(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ BSTR Replace,
			/*[in]*/ long vsFindOptionsValue,
			/*[in,out]*/ struct TextRanges * * Tags,
			/*[out,retval]*/ VARIANT_BOOL * pbRetVal) = 0;
		virtual HRESULT __stdcall CreateEditPoint(
			/*[in]*/ struct TextPoint * TextPoint,
			/*[out,retval]*/ struct EditPoint * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * pStartPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * pEndPoint) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall put_Language(
			/*[in]*/ BSTR pLanguage) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ BSTR * pType) = 0;
		virtual HRESULT __stdcall get_IndentSize(
			/*[out,retval]*/ long * pSize) = 0;
		virtual HRESULT __stdcall get_TabSize(
			/*[out,retval]*/ long * pSize) = 0;
		virtual HRESULT __stdcall ReplaceText(
			/*[in]*/ BSTR FindText,
			/*[in]*/ BSTR ReplaceText,
			/*[in]*/ long vsFindOptionsValue,
			/*[out,retval]*/ VARIANT_BOOL * pbRetVal) = 0;
		virtual HRESULT __stdcall PrintOut() = 0;
	};

	struct __declspec(uuid("509b9955-7303-48c9-90d4-e165b974e6ba"))
		CommandWindow : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * pParent) = 0;
		virtual HRESULT __stdcall SendInput(
			BSTR Command,
			VARIANT_BOOL Execute) = 0;
		virtual HRESULT __stdcall OutputString(
			BSTR Text) = 0;
		virtual HRESULT __stdcall Clear() = 0;
		virtual HRESULT __stdcall get_TextDocument(
			/*[out,retval]*/ struct TextDocument * * ppTextDocument) = 0;
	};

	struct __declspec(uuid("b3c38885-b288-44a8-b290-34fe63bf3c76"))
		_TextEditorEventsRoot : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_TextEditorEvents(
			/*[in]*/ struct TextDocument * TextDocumentFilter,
			/*[out,retval]*/ IDispatch * * ppDisp) = 0;
	};

	struct __declspec(uuid("63eb5c39-ca8f-498e-9a66-6dd4a27ac95b"))
		Document : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ BSTR * pKind) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Documents * * DocumentsObject) = 0;
		virtual HRESULT __stdcall get_ActiveWindow(
			/*[out,retval]*/ struct Window * * pWindow) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall get_Path(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall get_ReadOnly(
			/*[out,retval]*/ VARIANT_BOOL * pRetval) = 0;
		virtual HRESULT __stdcall get_Saved(
			/*[out,retval]*/ VARIANT_BOOL * pRetval) = 0;
		virtual HRESULT __stdcall put_Saved(
			/*[in]*/ VARIANT_BOOL pRetval) = 0;
		virtual HRESULT __stdcall get_Windows(
			/*[out,retval]*/ struct Windows * * pWindows) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pRetval) = 0;
		virtual HRESULT __stdcall Activate() = 0;
		virtual HRESULT __stdcall Close(
			/*[in]*/ enum vsSaveChanges Save) = 0;
		virtual HRESULT __stdcall NewWindow(
			/*[out,retval]*/ struct Window * * pWindow) = 0;
		virtual HRESULT __stdcall Redo(
			/*[out,retval]*/ VARIANT_BOOL * pbRetVal) = 0;
		virtual HRESULT __stdcall Undo(
			/*[out,retval]*/ VARIANT_BOOL * pbRetVal) = 0;
		virtual HRESULT __stdcall Save(
			/*[in]*/ BSTR FileName,
			/*[out,retval]*/ enum vsSaveStatus * pStatus) = 0;
		virtual HRESULT __stdcall get_Selection(
			/*[out,retval]*/ IDispatch * * SelectionObject) = 0;
		virtual HRESULT __stdcall Object(
			/*[in]*/ BSTR ModelKind,
			/*[out,retval]*/ IDispatch * * DataModelObject) = 0;
		virtual HRESULT __stdcall get_Extender(
			/*[in]*/ BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall PrintOut() = 0;
		virtual HRESULT __stdcall get_IndentSize(
			/*[out,retval]*/ long * pSize) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall put_Language(
			/*[in]*/ BSTR pLanguage) = 0;
		virtual HRESULT __stdcall put_ReadOnly(
			/*[in]*/ VARIANT_BOOL pRetval) = 0;
		virtual HRESULT __stdcall get_TabSize(
			/*[out,retval]*/ long * pSize) = 0;
		virtual HRESULT __stdcall ClearBookmarks() = 0;
		virtual HRESULT __stdcall MarkText(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ long Flags,
			/*[out,retval]*/ VARIANT_BOOL * pbRetVal) = 0;
		virtual HRESULT __stdcall ReplaceText(
			/*[in]*/ BSTR FindText,
			/*[in]*/ BSTR ReplaceText,
			/*[in]*/ long Flags,
			/*[out,retval]*/ VARIANT_BOOL * pbRetVal) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ BSTR * pType) = 0;
	};

	struct __declspec(uuid("9e2cf3ea-140f-413e-bd4b-7d46740cd2f4"))
		Documents : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Document * * DocumentObject) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * CountOfDocuments) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Kind,
			/*[out,retval]*/ struct Document * * ppDocument) = 0;
		virtual HRESULT __stdcall CloseAll(
			/*[in]*/ enum vsSaveChanges Save) = 0;
		virtual HRESULT __stdcall SaveAll() = 0;
		virtual HRESULT __stdcall Open(
			BSTR PathName,
			BSTR Kind,
			VARIANT_BOOL ReadOnly,
			/*[out,retval]*/ struct Document * * ppDocument) = 0;
	};

	struct __declspec(uuid("134170f8-93b1-42dd-9f89-a2ac7010ba07"))
		Events : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_CommandBarEvents(
			/*[in]*/ IDispatch * CommandBarControl,
			/*[out,retval]*/ IDispatch * * prceNew) = 0;
		virtual HRESULT __stdcall get_CommandEvents(
			/*[in]*/ BSTR Guid,
			/*[in]*/ long ID,
			/*[out,retval]*/ struct _CommandEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_SelectionEvents(
			/*[out,retval]*/ struct _SelectionEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_SolutionEvents(
			/*[out,retval]*/ struct _SolutionEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_BuildEvents(
			/*[out,retval]*/ struct _BuildEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_WindowEvents(
			/*[in]*/ struct Window * WindowFilter,
			/*[out,retval]*/ struct _WindowEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_OutputWindowEvents(
			/*[in]*/ BSTR Pane,
			/*[out,retval]*/ struct _OutputWindowEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_FindEvents(
			/*[out,retval]*/ struct _FindEvents * * ppFindEvents) = 0;
		virtual HRESULT __stdcall get_TaskListEvents(
			/*[in]*/ BSTR Filter,
			/*[out,retval]*/ struct _TaskListEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_DTEEvents(
			/*[out,retval]*/ struct _DTEEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_DocumentEvents(
			/*[in]*/ struct Document * Document,
			/*[out,retval]*/ struct _DocumentEvents * * ppceNew) = 0;
		virtual HRESULT __stdcall get_SolutionItemsEvents(
			/*[out,retval]*/ struct _ProjectItemsEvents * * ppeNew) = 0;
		virtual HRESULT __stdcall get_MiscFilesEvents(
			/*[out,retval]*/ struct _ProjectItemsEvents * * ppeNew) = 0;
		virtual HRESULT __stdcall get_DebuggerEvents(
			/*[out,retval]*/ struct _DebuggerEvents * * ppeNew) = 0;
		virtual HRESULT __stdcall get_TextEditorEvents(
			/*[in]*/ struct TextDocument * TextDocumentFilter,
			/*[out,retval]*/ struct _TextEditorEvents * * ppeNew) = 0;
		virtual HRESULT __stdcall GetObject(
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ IDispatch * * ppObject) = 0;
	};

	struct __declspec(uuid("3c536122-57b1-46de-ab34-acc524140093"))
		IVsExtensibility : IUnknown
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Properties(
			/*[in]*/ struct ISupportVSProperties * pParent,
			/*[in]*/ IDispatch * pdispPropObj,
			/*[out]*/ struct Properties * * ppProperties) = 0;
		virtual HRESULT __stdcall RunWizardFile(
			/*[in]*/ BSTR bstrWizFilename,
			/*[in]*/ long hwndOwner,
			/*[in]*/ SAFEARRAY * * vContextParams,
			/*[out,retval]*/ enum wizardResult * pResult) = 0;
		virtual HRESULT __stdcall Get_TextBuffer(
			/*[in]*/ IUnknown * pVsTextStream,
			/*[in]*/ struct IExtensibleObjectSite * pParent,
			/*[out,retval]*/ struct TextBuffer * * ppTextBuffer) = 0;
		virtual HRESULT __stdcall EnterAutomationFunction() = 0;
		virtual HRESULT __stdcall ExitAutomationFunction() = 0;
		virtual HRESULT __stdcall IsInAutomationFunction(
			/*[out,retval]*/ long * pfInAutoFunc) = 0;
		virtual HRESULT __stdcall GetUserControl(
			/*[out]*/ VARIANT_BOOL * fUserControl) = 0;
		virtual HRESULT __stdcall SetUserControl(
			/*[in]*/ VARIANT_BOOL fUserControl) = 0;
		virtual HRESULT __stdcall SetUserControlUnlatched(
			/*[in]*/ VARIANT_BOOL fUserControl) = 0;
		virtual HRESULT __stdcall LockServer(
			/*[in]*/ VARIANT_BOOL __MIDL__IVsExtensibility0000) = 0;
		virtual HRESULT __stdcall GetLockCount(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall TestForShutdown(
			/*[out,retval]*/ VARIANT_BOOL * fShutdown) = 0;
		virtual HRESULT __stdcall GetGlobalsObject(
			/*[in]*/ VARIANT ExtractFrom,
			/*[out,retval]*/ struct Globals * * ppGlobals) = 0;
		virtual HRESULT __stdcall GetConfigMgr(
			/*[in]*/ IUnknown * pIVsProject,
			ULONG_PTR itemid,
			/*[out,retval]*/ struct ConfigurationManager * * ppCfgMgr) = 0;
		virtual HRESULT __stdcall FireMacroReset() = 0;
		virtual HRESULT __stdcall GetDocumentFromDocCookie(
			LONG_PTR lDocCookie,
			/*[out,retval]*/ struct Document * * ppDoc) = 0;
		virtual HRESULT __stdcall IsMethodDisabled(
			GUID * pGUID,
			long dispid) = 0;
		virtual HRESULT __stdcall SetSuppressUI(
			VARIANT_BOOL In) = 0;
		virtual HRESULT __stdcall GetSuppressUI(
			VARIANT_BOOL * pOut) = 0;
	};

	struct __declspec(uuid("dc5437f4-f114-11d2-aacf-00c04f688dde"))
		_DocumentEventsRoot : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DocumentEvents(
			/*[in]*/ struct Document * WindowFilter,
			/*[out,retval]*/ IDispatch * * ppDisp) = 0;
	};

	struct __declspec(uuid("1fa0e135-399a-4d2c-a4fe-d21e2480f921"))
		TextSelection : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct TextDocument * * ppParent) = 0;
		virtual HRESULT __stdcall get_AnchorPoint(
			/*[out,retval]*/ struct VirtualPoint * * ppPoint) = 0;
		virtual HRESULT __stdcall get_ActivePoint(
			/*[out,retval]*/ struct VirtualPoint * * ppPoint) = 0;
		virtual HRESULT __stdcall get_AnchorColumn(
			/*[out,retval]*/ long * pColumn) = 0;
		virtual HRESULT __stdcall get_BottomLine(
			/*[out,retval]*/ long * pLine) = 0;
		virtual HRESULT __stdcall get_BottomPoint(
			/*[out,retval]*/ struct VirtualPoint * * ppPoint) = 0;
		virtual HRESULT __stdcall get_CurrentColumn(
			/*[out,retval]*/ long * pColumn) = 0;
		virtual HRESULT __stdcall get_CurrentLine(
			/*[out,retval]*/ long * pLine) = 0;
		virtual HRESULT __stdcall get_IsEmpty(
			/*[out,retval]*/ VARIANT_BOOL * pfEmpty) = 0;
		virtual HRESULT __stdcall get_IsActiveEndGreater(
			/*[out,retval]*/ VARIANT_BOOL * pfGreater) = 0;
		virtual HRESULT __stdcall get_Text(
			/*[out,retval]*/ BSTR * pText) = 0;
		virtual HRESULT __stdcall put_Text(
			/*[in]*/ BSTR pText) = 0;
		virtual HRESULT __stdcall get_TopLine(
			/*[out,retval]*/ long * pLine) = 0;
		virtual HRESULT __stdcall get_TopPoint(
			/*[out,retval]*/ struct VirtualPoint * * ppPoint) = 0;
		virtual HRESULT __stdcall ChangeCase(
			/*[in]*/ enum vsCaseOptions How) = 0;
		virtual HRESULT __stdcall CharLeft(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall CharRight(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall ClearBookmark() = 0;
		virtual HRESULT __stdcall Collapse() = 0;
		virtual HRESULT __stdcall OutlineSection() = 0;
		virtual HRESULT __stdcall Copy() = 0;
		virtual HRESULT __stdcall Cut() = 0;
		virtual HRESULT __stdcall Paste() = 0;
		virtual HRESULT __stdcall Delete(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall DeleteLeft(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall DeleteWhitespace(
			/*[in]*/ enum vsWhitespaceOptions Direction) = 0;
		virtual HRESULT __stdcall EndOfLine(
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall StartOfLine(
			/*[in]*/ enum vsStartOfLineOptions Where,
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall EndOfDocument(
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall StartOfDocument(
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall FindPattern(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ long vsFindOptionsValue,
			/*[in,out]*/ struct TextRanges * * Tags,
			/*[out,retval]*/ VARIANT_BOOL * pfFound) = 0;
		virtual HRESULT __stdcall ReplacePattern(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ BSTR Replace,
			/*[in]*/ long vsFindOptionsValue,
			/*[in,out]*/ struct TextRanges * * Tags,
			/*[out,retval]*/ VARIANT_BOOL * pfFound) = 0;
		virtual HRESULT __stdcall FindText(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ long vsFindOptionsValue,
			/*[out,retval]*/ VARIANT_BOOL * pfFound) = 0;
		virtual HRESULT __stdcall ReplaceText(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ BSTR Replace,
			/*[in]*/ long vsFindOptionsValue,
			/*[out,retval]*/ VARIANT_BOOL * pfFound) = 0;
		virtual HRESULT __stdcall GotoLine(
			/*[in]*/ long Line,
			/*[in]*/ VARIANT_BOOL Select) = 0;
		virtual HRESULT __stdcall Indent(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall Unindent(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall Insert(
			/*[in]*/ BSTR Text,
			/*[in]*/ long vsInsertFlagsCollapseToEndValue) = 0;
		virtual HRESULT __stdcall InsertFromFile(
			/*[in]*/ BSTR File) = 0;
		virtual HRESULT __stdcall LineDown(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall LineUp(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall MoveToPoint(
			/*[in]*/ struct TextPoint * Point,
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall MoveToLineAndOffset(
			/*[in]*/ long Line,
			/*[in]*/ long Offset,
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall MoveToAbsoluteOffset(
			/*[in]*/ long Offset,
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall NewLine(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall SetBookmark() = 0;
		virtual HRESULT __stdcall NextBookmark(
			/*[out,retval]*/ VARIANT_BOOL * pbFound) = 0;
		virtual HRESULT __stdcall PreviousBookmark(
			/*[out,retval]*/ VARIANT_BOOL * pbFound) = 0;
		virtual HRESULT __stdcall PadToColumn(
			/*[in]*/ long Column) = 0;
		virtual HRESULT __stdcall SmartFormat() = 0;
		virtual HRESULT __stdcall SelectAll() = 0;
		virtual HRESULT __stdcall SelectLine() = 0;
		virtual HRESULT __stdcall SwapAnchor() = 0;
		virtual HRESULT __stdcall Tabify() = 0;
		virtual HRESULT __stdcall Untabify() = 0;
		virtual HRESULT __stdcall WordLeft(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall WordRight(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall get_TextPane(
			/*[out,retval]*/ struct TextPane * * ppPane) = 0;
		virtual HRESULT __stdcall get_Mode(
			/*[out,retval]*/ enum vsSelectionMode * pMode) = 0;
		virtual HRESULT __stdcall put_Mode(
			/*[in]*/ enum vsSelectionMode pMode) = 0;
		virtual HRESULT __stdcall get_TextRanges(
			/*[out,retval]*/ struct TextRanges * * ppRanges) = 0;
		virtual HRESULT __stdcall Backspace(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall Cancel() = 0;
		virtual HRESULT __stdcall DestructiveInsert(
			/*[in]*/ BSTR Text) = 0;
		virtual HRESULT __stdcall MoveTo(
			/*[in]*/ long Line,
			/*[in]*/ long Column,
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall MoveToDisplayColumn(
			/*[in]*/ long Line,
			/*[in]*/ long Column,
			/*[in]*/ VARIANT_BOOL Extend) = 0;
		virtual HRESULT __stdcall PageUp(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall PageDown(
			/*[in]*/ VARIANT_BOOL Extend,
			/*[in]*/ long Count) = 0;
	};

	struct __declspec(uuid("b6422e9c-9efd-4f87-bddc-c7fd8f2fd303"))
		TextRanges : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct TextDocument * * ppParent) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct TextRange * * ppRange) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
	};

	struct __declspec(uuid("c1ffe800-028b-4475-a907-14f51f19bb7d"))
		EditPoint : TextPoint
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall CharLeft(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall CharRight(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall EndOfLine() = 0;
		virtual HRESULT __stdcall StartOfLine() = 0;
		virtual HRESULT __stdcall EndOfDocument() = 0;
		virtual HRESULT __stdcall StartOfDocument() = 0;
		virtual HRESULT __stdcall WordLeft(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall WordRight(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall LineUp(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall LineDown(
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall MoveToPoint(
			/*[in]*/ struct TextPoint * Point) = 0;
		virtual HRESULT __stdcall MoveToLineAndOffset(
			/*[in]*/ long Line,
			/*[in]*/ long Offset) = 0;
		virtual HRESULT __stdcall MoveToAbsoluteOffset(
			/*[in]*/ long Offset) = 0;
		virtual HRESULT __stdcall SetBookmark() = 0;
		virtual HRESULT __stdcall ClearBookmark() = 0;
		virtual HRESULT __stdcall NextBookmark(
			/*[out,retval]*/ VARIANT_BOOL * pbFound) = 0;
		virtual HRESULT __stdcall PreviousBookmark(
			/*[out,retval]*/ VARIANT_BOOL * pbFound) = 0;
		virtual HRESULT __stdcall PadToColumn(
			/*[in]*/ long Column) = 0;
		virtual HRESULT __stdcall Insert(
			/*[in]*/ BSTR Text) = 0;
		virtual HRESULT __stdcall InsertFromFile(
			/*[in]*/ BSTR File) = 0;
		virtual HRESULT __stdcall GetText(
			/*[in]*/ VARIANT PointOrCount,
			/*[out,retval]*/ BSTR * pbstrText) = 0;
		virtual HRESULT __stdcall GetLines(
			/*[in]*/ long Start,
			/*[in]*/ long ExclusiveEnd,
			/*[out,retval]*/ BSTR * pbstrText) = 0;
		virtual HRESULT __stdcall Copy(
			/*[in]*/ VARIANT PointOrCount,
			/*[in]*/ VARIANT_BOOL Append) = 0;
		virtual HRESULT __stdcall Cut(
			/*[in]*/ VARIANT PointOrCount,
			/*[in]*/ VARIANT_BOOL Append) = 0;
		virtual HRESULT __stdcall Delete(
			/*[in]*/ VARIANT PointOrCount) = 0;
		virtual HRESULT __stdcall Paste() = 0;
		virtual HRESULT __stdcall ReadOnly(
			/*[in]*/ VARIANT PointOrCount,
			/*[out,retval]*/ VARIANT_BOOL * lfResult) = 0;
		virtual HRESULT __stdcall FindPattern(
			/*[in]*/ BSTR Pattern,
			/*[in]*/ long vsFindOptionsValue,
			/*[in,out]*/ struct EditPoint * * EndPoint,
			/*[in,out]*/ struct TextRanges * * Tags,
			/*[out,retval]*/ VARIANT_BOOL * pbFound) = 0;
		virtual HRESULT __stdcall ReplacePattern(
			/*[in]*/ struct TextPoint * Point,
			/*[in]*/ BSTR Pattern,
			/*[in]*/ BSTR Replace,
			/*[in]*/ long vsFindOptionsValue,
			/*[in,out]*/ struct TextRanges * * Tags,
			/*[out,retval]*/ VARIANT_BOOL * pbFound) = 0;
		virtual HRESULT __stdcall Indent(
			/*[in]*/ struct TextPoint * Point,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall Unindent(
			/*[in]*/ struct TextPoint * Point,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall SmartFormat(
			/*[in]*/ struct TextPoint * Point) = 0;
		virtual HRESULT __stdcall OutlineSection(
			/*[in]*/ VARIANT PointOrCount) = 0;
		virtual HRESULT __stdcall ReplaceText(
			/*[in]*/ VARIANT PointOrCount,
			/*[in]*/ BSTR Text,
			/*[in]*/ long Flags) = 0;
		virtual HRESULT __stdcall ChangeCase(
			/*[in]*/ VARIANT PointOrCount,
			/*[in]*/ enum vsCaseOptions How) = 0;
		virtual HRESULT __stdcall DeleteWhitespace(
			/*[in]*/ enum vsWhitespaceOptions Direction) = 0;
	};

	struct __declspec(uuid("72767524-e3b3-43d0-bb46-bbe1d556a9ff"))
		TextRange : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct TextRanges * * ppParent) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct EditPoint * * ppPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct EditPoint * * ppPoint) = 0;
	};

	struct __declspec(uuid("0a3bf283-05f8-4669-9bcb-a84b6423349a"))
		TextPane : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct TextPanes * * ppPanes) = 0;
		virtual HRESULT __stdcall get_Window(
			/*[out,retval]*/ struct Window * * ppWin) = 0;
		virtual HRESULT __stdcall get_Height(
			/*[out,retval]*/ long * pHeight) = 0;
		virtual HRESULT __stdcall get_Width(
			/*[out,retval]*/ long * pWidth) = 0;
		virtual HRESULT __stdcall get_Selection(
			/*[out,retval]*/ struct TextSelection * * ppSel) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppPoint) = 0;
		virtual HRESULT __stdcall Activate() = 0;
		virtual HRESULT __stdcall IsVisible(
			/*[in]*/ struct TextPoint * Point,
			/*[in]*/ VARIANT PointOrCount,
			/*[out,retval]*/ VARIANT_BOOL * pbResult) = 0;
		virtual HRESULT __stdcall TryToShow(
			/*[in]*/ struct TextPoint * Point,
			/*[in]*/ enum vsPaneShowHow How,
			/*[in]*/ VARIANT PointOrCount,
			/*[out,retval]*/ VARIANT_BOOL * pbResult) = 0;
	};

	struct __declspec(uuid("d9013d31-3652-46b2-a25a-29a881b9f86b"))
		TextPanes : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct TextWindow * * ppParent) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct TextPane * * ppPane) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
	};

	struct __declspec(uuid("2fc54dc9-922b-44eb-8cc0-ba182584dc4b"))
		TextWindow : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * ppParent) = 0;
		virtual HRESULT __stdcall get_Selection(
			/*[out,retval]*/ struct TextSelection * * ppSel) = 0;
		virtual HRESULT __stdcall get_ActivePane(
			/*[out,retval]*/ struct TextPane * * ppPane) = 0;
		virtual HRESULT __stdcall get_Panes(
			/*[out,retval]*/ struct TextPanes * * ppPanes) = 0;
	};

	struct __declspec(uuid("0cfbc2b7-0d4e-11d3-8997-00c04f688dde"))
		CodeType : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Namespace(
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall get_Bases(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Members(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess pAccess) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * pAccess) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddBase(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeElement * * ppOut) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall RemoveBase(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall RemoveMember(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall get_IsDerivedFrom(
			BSTR FullName,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_DerivedTypes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
	};

	struct __declspec(uuid("0cfbc2bc-0d4e-11d3-8997-00c04f688dde"))
		CodeTypeRef : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_TypeKind(
			/*[out,retval]*/ enum vsCMTypeRef * pType) = 0;
		virtual HRESULT __stdcall get_CodeType(
			/*[out,retval]*/ struct CodeType * * ppCodeType) = 0;
		virtual HRESULT __stdcall put_CodeType(
			struct CodeType * ppCodeType) = 0;
		virtual HRESULT __stdcall get_ElementType(
			/*[out,retval]*/ struct CodeTypeRef * * ppCodeTypeRef) = 0;
		virtual HRESULT __stdcall put_ElementType(
			struct CodeTypeRef * ppCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_AsString(
			/*[out,retval]*/ BSTR * pAsString) = 0;
		virtual HRESULT __stdcall get_AsFullName(
			/*[out,retval]*/ BSTR * pAsFullName) = 0;
		virtual HRESULT __stdcall get_Rank(
			/*[out,retval]*/ long * pRank) = 0;
		virtual HRESULT __stdcall put_Rank(
			long pRank) = 0;
		virtual HRESULT __stdcall CreateArrayType(
			long Rank,
			/*[out,retval]*/ struct CodeTypeRef * * ppTypeRef) = 0;
	};

	struct __declspec(uuid("0cfbc2bd-0d4e-11d3-8997-00c04f688dde"))
		CodeParameter : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct CodeElement * * ppCodeElement) = 0;
		virtual HRESULT __stdcall put_Type(
			struct CodeTypeRef * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ struct CodeTypeRef * * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppMembers) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
	};

	struct __declspec(uuid("0cfbc2b9-0d4e-11d3-8997-00c04f688dde"))
		CodeFunction : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_FunctionKind(
			/*[out,retval]*/ enum vsCMFunction * ppFunctionKind) = 0;
		virtual HRESULT __stdcall get_Prototype(
			/*[in]*/ long Flags,
			/*[out,retval]*/ BSTR * pFullName) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ struct CodeTypeRef * * ppCodeTypeRef) = 0;
		virtual HRESULT __stdcall put_Type(
			struct CodeTypeRef * ppCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_Parameters(
			/*[out,retval]*/ struct CodeElements * * ppMembers) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess Access) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * Access) = 0;
		virtual HRESULT __stdcall get_IsOverloaded(
			/*[out,retval]*/ VARIANT_BOOL * pvbOverloaded) = 0;
		virtual HRESULT __stdcall get_IsShared(
			/*[out,retval]*/ VARIANT_BOOL * Shared) = 0;
		virtual HRESULT __stdcall put_IsShared(
			VARIANT_BOOL Shared) = 0;
		virtual HRESULT __stdcall get_MustImplement(
			/*[out,retval]*/ VARIANT_BOOL * MustImplement) = 0;
		virtual HRESULT __stdcall put_MustImplement(
			VARIANT_BOOL MustImplement) = 0;
		virtual HRESULT __stdcall get_Overloads(
			/*[out,retval]*/ struct CodeElements * * ppMembers) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppMembers) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddParameter(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			/*[out,retval]*/ struct CodeParameter * * ppCodeParameter) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall RemoveParameter(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall get_CanOverride(
			/*[out,retval]*/ VARIANT_BOOL * pCanOverride) = 0;
		virtual HRESULT __stdcall put_CanOverride(
			VARIANT_BOOL pCanOverride) = 0;
	};

	struct __declspec(uuid("0cfbc2ba-0d4e-11d3-8997-00c04f688dde"))
		CodeVariable : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_InitExpression(
			/*[out,retval]*/ VARIANT * pExpr) = 0;
		virtual HRESULT __stdcall put_InitExpression(
			VARIANT pExpr) = 0;
		virtual HRESULT __stdcall get_Prototype(
			/*[in]*/ long Flags,
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Type(
			struct CodeTypeRef * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ struct CodeTypeRef * * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess Access) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * Access) = 0;
		virtual HRESULT __stdcall get_IsConstant(
			/*[out,retval]*/ VARIANT_BOOL * pIsConstant) = 0;
		virtual HRESULT __stdcall put_IsConstant(
			VARIANT_BOOL pIsConstant) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppMembers) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall get_IsShared(
			/*[out,retval]*/ VARIANT_BOOL * pShared) = 0;
		virtual HRESULT __stdcall put_IsShared(
			VARIANT_BOOL pShared) = 0;
	};

	struct __declspec(uuid("0cfbc2b8-0d4e-11d3-8997-00c04f688dde"))
		CodeNamespace : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Members(
			/*[out,retval]*/ struct CodeElements * * ppMembers) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddNamespace(
			BSTR Name,
			VARIANT Position,
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall AddClass(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeClass * * ppCodeClass) = 0;
		virtual HRESULT __stdcall AddInterface(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeInterface * * ppCodeInterface) = 0;
		virtual HRESULT __stdcall AddStruct(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeStruct * * ppCodeStruct) = 0;
		virtual HRESULT __stdcall AddEnum(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeEnum * * ppCodeEnum) = 0;
		virtual HRESULT __stdcall AddDelegate(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeDelegate * * ppCodeDelegate) = 0;
		virtual HRESULT __stdcall Remove(
			VARIANT Element) = 0;
	};

	struct __declspec(uuid("b1f42512-91cd-4d3a-8b25-a317d8032b24"))
		CodeEnum : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Namespace(
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall get_Bases(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Members(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess pAccess) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * pAccess) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddBase(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeElement * * ppOut) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall RemoveBase(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall RemoveMember(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall get_IsDerivedFrom(
			BSTR FullName,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_DerivedTypes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall AddMember(
			BSTR Name,
			VARIANT Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeVariable * * ppCodeElements) = 0;
	};

	struct __declspec(uuid("b1f42514-91cd-4d3a-8b25-a317d8032b24"))
		CodeClass : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Namespace(
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall get_Bases(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Members(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess pAccess) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * pAccess) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddBase(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeElement * * ppOut) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall RemoveBase(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall RemoveMember(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall get_IsDerivedFrom(
			BSTR FullName,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_DerivedTypes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_ImplementedInterfaces(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_IsAbstract(
			/*[out,retval]*/ VARIANT_BOOL * pIsAbstract) = 0;
		virtual HRESULT __stdcall put_IsAbstract(
			VARIANT_BOOL pIsAbstract) = 0;
		virtual HRESULT __stdcall AddImplementedInterface(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeInterface * * ppCodeInterface) = 0;
		virtual HRESULT __stdcall AddFunction(
			BSTR Name,
			enum vsCMFunction Kind,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			VARIANT Location,
			/*[out,retval]*/ struct CodeFunction * * ppCodeFunction) = 0;
		virtual HRESULT __stdcall AddVariable(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			VARIANT Location,
			/*[out,retval]*/ struct CodeVariable * * ppCodeVariable) = 0;
		virtual HRESULT __stdcall AddProperty(
			BSTR GetterName,
			BSTR PutterName,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			VARIANT Location,
			/*[out,retval]*/ struct CodeProperty * * ppCodeProperty) = 0;
		virtual HRESULT __stdcall AddClass(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeClass * * ppCodeClass) = 0;
		virtual HRESULT __stdcall AddStruct(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeStruct * * ppCodeStruct) = 0;
		virtual HRESULT __stdcall AddEnum(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeEnum * * ppCodeEnum) = 0;
		virtual HRESULT __stdcall AddDelegate(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeDelegate * * ppCodeDelegate) = 0;
		virtual HRESULT __stdcall RemoveInterface(
			VARIANT Element) = 0;
	};

	struct __declspec(uuid("0cfbc2bb-0d4e-11d3-8997-00c04f688dde"))
		CodeProperty : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct CodeClass * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Prototype(
			/*[in]*/ long Flags,
			/*[out,retval]*/ BSTR * pFullName) = 0;
		virtual HRESULT __stdcall put_Type(
			struct CodeTypeRef * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ struct CodeTypeRef * * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_Getter(
			/*[out,retval]*/ struct CodeFunction * * ppCodeFunction) = 0;
		virtual HRESULT __stdcall put_Getter(
			struct CodeFunction * ppCodeFunction) = 0;
		virtual HRESULT __stdcall get_Setter(
			/*[out,retval]*/ struct CodeFunction * * ppCodeFunction) = 0;
		virtual HRESULT __stdcall put_Setter(
			struct CodeFunction * ppCodeFunction) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess Access) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * Access) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppMembers) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
	};

	struct __declspec(uuid("b1f42510-91cd-4d3a-8b25-a317d8032b24"))
		CodeInterface : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Namespace(
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall get_Bases(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Members(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess pAccess) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * pAccess) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddBase(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeElement * * ppOut) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall RemoveBase(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall RemoveMember(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall get_IsDerivedFrom(
			BSTR FullName,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_DerivedTypes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall AddFunction(
			BSTR Name,
			enum vsCMFunction Kind,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeFunction * * ppCodeFunction) = 0;
		virtual HRESULT __stdcall AddProperty(
			BSTR GetterName,
			BSTR PutterName,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			VARIANT Location,
			/*[out,retval]*/ struct CodeProperty * * ppCodeProperty) = 0;
	};

	struct __declspec(uuid("b1f42513-91cd-4d3a-8b25-a317d8032b24"))
		CodeDelegate : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Namespace(
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall get_Bases(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Members(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess pAccess) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * pAccess) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddBase(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeElement * * ppOut) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall RemoveBase(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall RemoveMember(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall get_IsDerivedFrom(
			BSTR FullName,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_DerivedTypes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_BaseClass(
			/*[out,retval]*/ struct CodeClass * * ppCodeClass) = 0;
		virtual HRESULT __stdcall get_Prototype(
			long Flags,
			/*[out,retval]*/ BSTR * pPrototype) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ struct CodeTypeRef * * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall put_Type(
			struct CodeTypeRef * pCodeTypeRef) = 0;
		virtual HRESULT __stdcall get_Parameters(
			/*[out,retval]*/ struct CodeElements * * ppParameters) = 0;
		virtual HRESULT __stdcall AddParameter(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			/*[out,retval]*/ struct CodeParameter * * ppCodeParameter) = 0;
		virtual HRESULT __stdcall RemoveParameter(
			VARIANT Element) = 0;
	};

	struct __declspec(uuid("b1f42511-91cd-4d3a-8b25-a317d8032b24"))
		CodeStruct : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct CodeElements * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall put_Name(
			BSTR pVal) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Kind(
			/*[out,retval]*/ enum vsCMElement * pCodeEltKind) = 0;
		virtual HRESULT __stdcall get_IsCodeType(
			/*[out,retval]*/ VARIANT_BOOL * pIsCodeType) = 0;
		virtual HRESULT __stdcall get_InfoLocation(
			/*[out,retval]*/ enum vsCMInfoLocation * pInfoLocation) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_StartPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_EndPoint(
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_Extender(
			BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall GetStartPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall GetEndPoint(
			/*[in]*/ enum vsCMPart Part,
			/*[out,retval]*/ struct TextPoint * * ppTextPoint) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ParentObject) = 0;
		virtual HRESULT __stdcall get_Namespace(
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall get_Bases(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_Members(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall put_Access(
			enum vsCMAccess pAccess) = 0;
		virtual HRESULT __stdcall get_Access(
			/*[out,retval]*/ enum vsCMAccess * pAccess) = 0;
		virtual HRESULT __stdcall get_Attributes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_DocComment(
			/*[out,retval]*/ BSTR * pDocComment) = 0;
		virtual HRESULT __stdcall put_DocComment(
			BSTR pDocComment) = 0;
		virtual HRESULT __stdcall get_Comment(
			/*[out,retval]*/ BSTR * pComment) = 0;
		virtual HRESULT __stdcall put_Comment(
			BSTR pComment) = 0;
		virtual HRESULT __stdcall AddBase(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeElement * * ppOut) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall RemoveBase(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall RemoveMember(
			VARIANT Element) = 0;
		virtual HRESULT __stdcall get_IsDerivedFrom(
			BSTR FullName,
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_DerivedTypes(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_ImplementedInterfaces(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall get_IsAbstract(
			/*[out,retval]*/ VARIANT_BOOL * pIsAbstract) = 0;
		virtual HRESULT __stdcall put_IsAbstract(
			VARIANT_BOOL pIsAbstract) = 0;
		virtual HRESULT __stdcall AddImplementedInterface(
			VARIANT Base,
			VARIANT Position,
			/*[out,retval]*/ struct CodeInterface * * ppCodeInterface) = 0;
		virtual HRESULT __stdcall AddFunction(
			BSTR Name,
			enum vsCMFunction Kind,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			VARIANT Location,
			/*[out,retval]*/ struct CodeFunction * * ppCodeFunction) = 0;
		virtual HRESULT __stdcall AddVariable(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			VARIANT Location,
			/*[out,retval]*/ struct CodeVariable * * ppCodeVariable) = 0;
		virtual HRESULT __stdcall AddProperty(
			BSTR GetterName,
			BSTR PutterName,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			VARIANT Location,
			/*[out,retval]*/ struct CodeProperty * * ppCodeProperty) = 0;
		virtual HRESULT __stdcall AddClass(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeClass * * ppCodeClass) = 0;
		virtual HRESULT __stdcall AddStruct(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeStruct * * ppCodeStruct) = 0;
		virtual HRESULT __stdcall AddEnum(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeEnum * * ppCodeEnum) = 0;
		virtual HRESULT __stdcall AddDelegate(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeDelegate * * ppCodeDelegate) = 0;
		virtual HRESULT __stdcall RemoveInterface(
			VARIANT Element) = 0;
	};

	struct __declspec(uuid("ed1a3f99-4477-11d3-89bf-00c04f688dde"))
		FileCodeModel : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct ProjectItem * * pProjItem) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * pLanguage) = 0;
		virtual HRESULT __stdcall get_CodeElements(
			/*[out,retval]*/ struct CodeElements * * ppCodeElements) = 0;
		virtual HRESULT __stdcall CodeElementFromPoint(
			struct TextPoint * Point,
			enum vsCMElement Scope,
			/*[out,retval]*/ struct CodeElement * * ppCodeElement) = 0;
		virtual HRESULT __stdcall AddNamespace(
			BSTR Name,
			VARIANT Position,
			/*[out,retval]*/ struct CodeNamespace * * ppCodeNamespace) = 0;
		virtual HRESULT __stdcall AddClass(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeClass * * ppCodeClass) = 0;
		virtual HRESULT __stdcall AddInterface(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeInterface * * ppCodeInterface) = 0;
		virtual HRESULT __stdcall AddFunction(
			BSTR Name,
			enum vsCMFunction Kind,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeFunction * * ppCodeFunction) = 0;
		virtual HRESULT __stdcall AddVariable(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeVariable * * ppCodeVariable) = 0;
		virtual HRESULT __stdcall AddAttribute(
			BSTR Name,
			BSTR Value,
			VARIANT Position,
			/*[out,retval]*/ struct CodeAttribute * * ppCodeAttribute) = 0;
		virtual HRESULT __stdcall AddStruct(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			VARIANT ImplementedInterfaces,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeStruct * * ppCodeStruct) = 0;
		virtual HRESULT __stdcall AddEnum(
			BSTR Name,
			VARIANT Position,
			VARIANT Bases,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeEnum * * ppCodeEnum) = 0;
		virtual HRESULT __stdcall AddDelegate(
			BSTR Name,
			VARIANT Type,
			VARIANT Position,
			enum vsCMAccess Access,
			/*[out,retval]*/ struct CodeDelegate * * ppCodeDelegate) = 0;
		virtual HRESULT __stdcall Remove(
			VARIANT Element) = 0;
	};

	struct __declspec(uuid("33c5ebb8-244e-449d-9cee-fad70a774e59"))
		ContextAttributes : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall Item(
			VARIANT index,
			/*[out,retval]*/ struct ContextAttribute * * ppVal) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
		virtual HRESULT __stdcall Add(
			BSTR AttributeName,
			BSTR AttributeValue,
			enum vsContextAttributeType Type,
			/*[out,retval]*/ struct ContextAttribute * * ppVal) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ enum vsContextAttributes * Type) = 0;
		virtual HRESULT __stdcall get_HighPriorityAttributes(
			/*[out,retval]*/ struct ContextAttributes * * ppVal) = 0;
		virtual HRESULT __stdcall Refresh() = 0;
	};

	struct __declspec(uuid("1a6e2cb3-b897-42eb-96be-ff0fdb65db2f"))
		ContextAttribute : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct ContextAttributes * * ppCollection) = 0;
		virtual HRESULT __stdcall get_Values(
			/*[out,retval]*/ VARIANT * pVal) = 0;
		virtual HRESULT __stdcall Remove() = 0;
	};

	struct __declspec(uuid("53a87fa1-ce93-48bf-958b-c6da793c5028"))
		AddIn : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Description(
			/*[out,retval]*/ BSTR * lpbstr) = 0;
		virtual HRESULT __stdcall put_Description(
			/*[in]*/ BSTR lpbstr) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct AddIns * * lppaddins) = 0;
		virtual HRESULT __stdcall get_ProgID(
			/*[out,retval]*/ BSTR * lpbstr) = 0;
		virtual HRESULT __stdcall get_Guid(
			/*[out,retval]*/ BSTR * lpbstr) = 0;
		virtual HRESULT __stdcall get_Connected(
			/*[out,retval]*/ VARIANT_BOOL * lpfConnect) = 0;
		virtual HRESULT __stdcall put_Connected(
			/*[in]*/ VARIANT_BOOL lpfConnect) = 0;
		virtual HRESULT __stdcall get_Object(
			/*[out,retval]*/ IDispatch * * lppdisp) = 0;
		virtual HRESULT __stdcall put_Object(
			/*[in]*/ IDispatch * lppdisp) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * lpbstr) = 0;
		virtual HRESULT __stdcall Remove() = 0;
		virtual HRESULT __stdcall get_SatelliteDllPath(
			/*[out,retval]*/ BSTR * pbstrPath) = 0;
	};

	struct __declspec(uuid("50590801-d13e-4404-80c2-5ca30a4d0ee8"))
		AddIns : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct AddIn * * lppaddin) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall Update() = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall Add(
			BSTR ProgID,
			BSTR Description,
			BSTR Name,
			VARIANT_BOOL Connected,
			/*[out,retval]*/ struct AddIn * * __MIDL__AddIns0000) = 0;
	};

	struct __declspec(uuid("ffc9dfc4-61ca-4b0c-83c2-0703a24f5c16"))
		OutputWindowPane : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct OutputWindowPanes * * pOutputWindowPanes) = 0;
		virtual HRESULT __stdcall get_Guid(
			/*[out,retval]*/ BSTR * pGUID) = 0;
		virtual HRESULT __stdcall OutputString(
			/*[in]*/ BSTR Text) = 0;
		virtual HRESULT __stdcall ForceItemsToTaskList() = 0;
		virtual HRESULT __stdcall Activate() = 0;
		virtual HRESULT __stdcall Clear() = 0;
		virtual HRESULT __stdcall OutputTaskItemString(
			/*[in]*/ BSTR Text,
			/*[in]*/ enum vsTaskPriority Priority,
			/*[in]*/ BSTR SubCategory,
			/*[in]*/ enum vsTaskIcon Icon,
			/*[in]*/ BSTR FileName,
			/*[in]*/ long Line,
			/*[in]*/ BSTR Description,
			/*[in]*/ VARIANT_BOOL Force) = 0;
		virtual HRESULT __stdcall get_TextDocument(
			/*[out,retval]*/ struct TextDocument * * pDocument) = 0;
	};

	struct __declspec(uuid("b02cf62a-9470-4308-a521-9675fba395ab"))
		OutputWindowPanes : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct OutputWindow * * pOutputWindow) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ struct OutputWindowPane * * pOutputWindowPane) = 0;
		virtual HRESULT __stdcall Item(
			VARIANT index,
			/*[out,retval]*/ struct OutputWindowPane * * pOutputWindowPane) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
	};

	struct __declspec(uuid("eab0a63d-c3a8-496e-9acf-a82cef6a43b3"))
		OutputWindow : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * pParent) = 0;
		virtual HRESULT __stdcall get_OutputWindowPanes(
			/*[out,retval]*/ struct OutputWindowPanes * * pOutputWindowPanes) = 0;
		virtual HRESULT __stdcall get_ActivePane(
			/*[out,retval]*/ struct OutputWindowPane * * pOutputWindowPane) = 0;
	};

	struct __declspec(uuid("58e4d419-6b8c-4c63-92de-70161cd95890"))
		TaskItem : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct TaskItems * * pTaskItems) = 0;
		virtual HRESULT __stdcall get_Category(
			/*[out,retval]*/ BSTR * pCategory) = 0;
		virtual HRESULT __stdcall get_SubCategory(
			/*[out,retval]*/ BSTR * pSubCategory) = 0;
		virtual HRESULT __stdcall get_Priority(
			/*[out,retval]*/ enum vsTaskPriority * pPriority) = 0;
		virtual HRESULT __stdcall put_Priority(
			/*[in]*/ enum vsTaskPriority pPriority) = 0;
		virtual HRESULT __stdcall get_Description(
			/*[out,retval]*/ BSTR * pDescription) = 0;
		virtual HRESULT __stdcall put_Description(
			/*[in]*/ BSTR pDescription) = 0;
		virtual HRESULT __stdcall get_FileName(
			/*[out,retval]*/ BSTR * pFileName) = 0;
		virtual HRESULT __stdcall put_FileName(
			/*[in]*/ BSTR pFileName) = 0;
		virtual HRESULT __stdcall get_IsSettable(
			/*[in]*/ enum vsTaskListColumn Column,
			/*[out,retval]*/ VARIANT_BOOL * pSettable) = 0;
		virtual HRESULT __stdcall get_Line(
			/*[out,retval]*/ long * pLine) = 0;
		virtual HRESULT __stdcall put_Line(
			/*[in]*/ long pLine) = 0;
		virtual HRESULT __stdcall get_Displayed(
			/*[out,retval]*/ VARIANT_BOOL * pDisplayed) = 0;
		virtual HRESULT __stdcall get_Checked(
			/*[out,retval]*/ VARIANT_BOOL * pChecked) = 0;
		virtual HRESULT __stdcall put_Checked(
			/*[in]*/ VARIANT_BOOL pChecked) = 0;
		virtual HRESULT __stdcall Navigate() = 0;
		virtual HRESULT __stdcall Delete() = 0;
		virtual HRESULT __stdcall Select() = 0;
	};

	struct __declspec(uuid("4b51103d-513c-4773-b56a-354d0928fd04"))
		TaskItems : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct TaskList * * pTaskList) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct TaskItem * * pTaskItem) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Category,
			/*[in]*/ BSTR SubCategory,
			/*[in]*/ BSTR Description,
			/*[in]*/ enum vsTaskPriority Priority,
			/*[in]*/ VARIANT Icon,
			/*[in]*/ VARIANT_BOOL Checkable,
			/*[in]*/ BSTR File,
			/*[in]*/ long Line,
			/*[in]*/ VARIANT_BOOL CanUserDelete,
			/*[in]*/ VARIANT_BOOL FlushItem,
			/*[out,retval]*/ struct TaskItem * * pTaskItem) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall ForceItemsToTaskList() = 0;
	};

	struct __declspec(uuid("4e4f0569-e16a-4da1-92de-10882a4ddd8c"))
		TaskList : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * lppReturn) = 0;
		virtual HRESULT __stdcall get_DefaultCommentToken(
			/*[out,retval]*/ BSTR * pbstrToken) = 0;
		virtual HRESULT __stdcall get_TaskItems(
			/*[out,retval]*/ struct TaskItems * * pTaskItems) = 0;
		virtual HRESULT __stdcall get_SelectedItems(
			/*[out,retval]*/ VARIANT * Selections) = 0;
	};

	struct __declspec(uuid("5c5a0070-f396-4e37-a82a-1b767e272df9"))
		Process : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Attach() = 0;
		virtual HRESULT __stdcall Detach(
			/*[in]*/ VARIANT_BOOL WaitForBreakOrEnd) = 0;
		virtual HRESULT __stdcall Break(
			/*[in]*/ VARIANT_BOOL WaitForBreakMode) = 0;
		virtual HRESULT __stdcall Terminate(
			/*[in]*/ VARIANT_BOOL WaitForBreakOrEnd) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * Name) = 0;
		virtual HRESULT __stdcall get_ProcessID(
			/*[out,retval]*/ long * ID) = 0;
		virtual HRESULT __stdcall get_Programs(
			/*[out,retval]*/ struct Programs * * Programs) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Processes * * Processes) = 0;
	};

	struct __declspec(uuid("dc6a118a-bbab-11d2-8ad1-00c04f79e479"))
		Programs : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Program * * Program) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
	};

	struct __declspec(uuid("6a38d87c-bba0-11d2-8ad1-00c04f79e479"))
		Program : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * Name) = 0;
		virtual HRESULT __stdcall get_Process(
			/*[out,retval]*/ struct Process * * Process) = 0;
		virtual HRESULT __stdcall get_Threads(
			/*[out,retval]*/ struct Threads * * Threads) = 0;
		virtual HRESULT __stdcall get_IsBeingDebugged(
			/*[out,retval]*/ VARIANT_BOOL * IsBeingDebugged) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Programs * * Programs) = 0;
	};

	struct __declspec(uuid("6aa23fb4-bba1-11d2-8ad1-00c04f79e479"))
		Threads : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Thread * * Thread) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
	};

	struct __declspec(uuid("9407f466-bba1-11d2-8ad1-00c04f79e479"))
		Thread : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Freeze() = 0;
		virtual HRESULT __stdcall Thaw() = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * Name) = 0;
		virtual HRESULT __stdcall get_SuspendCount(
			/*[out,retval]*/ long * Count) = 0;
		virtual HRESULT __stdcall get_ID(
			/*[out,retval]*/ long * ID) = 0;
		virtual HRESULT __stdcall get_StackFrames(
			/*[out,retval]*/ struct StackFrames * * StackFrames) = 0;
		virtual HRESULT __stdcall get_Program(
			/*[out,retval]*/ struct Program * * Program) = 0;
		virtual HRESULT __stdcall get_IsAlive(
			/*[out,retval]*/ VARIANT_BOOL * IsAlive) = 0;
		virtual HRESULT __stdcall get_Priority(
			/*[out,retval]*/ BSTR * Priority) = 0;
		virtual HRESULT __stdcall get_Location(
			/*[out,retval]*/ BSTR * Location) = 0;
		virtual HRESULT __stdcall get_IsFrozen(
			/*[out,retval]*/ VARIANT_BOOL * IsFrozen) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Threads * * Threads) = 0;
	};

	struct __declspec(uuid("4ed85664-bba2-11d2-8ad1-00c04f79e479"))
		StackFrames : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct StackFrame * * StackFrame) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
	};

	struct __declspec(uuid("1342d0d8-bba3-11d2-8ad1-00c04f79e479"))
		StackFrame : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * Language) = 0;
		virtual HRESULT __stdcall get_FunctionName(
			/*[out,retval]*/ BSTR * FunctionName) = 0;
		virtual HRESULT __stdcall get_ReturnType(
			/*[out,retval]*/ BSTR * ReturnType) = 0;
		virtual HRESULT __stdcall get_Locals(
			/*[out,retval]*/ struct Expressions * * Expressions) = 0;
		virtual HRESULT __stdcall get_Arguments(
			/*[out,retval]*/ struct Expressions * * Expressions) = 0;
		virtual HRESULT __stdcall get_Module(
			/*[out,retval]*/ BSTR * Module) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Thread * * Thread) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct StackFrames * * StackFrames) = 0;
	};

	struct __declspec(uuid("2685337a-bb9e-11d2-8ad1-00c04f79e479"))
		Expressions : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Expression * * Expression) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
	};

	struct __declspec(uuid("27adc812-bb07-11d2-8ad1-00c04f79e479"))
		Expression : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * Name) = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ BSTR * Type) = 0;
		virtual HRESULT __stdcall get_DataMembers(
			/*[out,retval]*/ struct Expressions * * Expressions) = 0;
		virtual HRESULT __stdcall get_Value(
			/*[out,retval]*/ BSTR * Value) = 0;
		virtual HRESULT __stdcall put_Value(
			/*[in]*/ BSTR Value) = 0;
		virtual HRESULT __stdcall get_IsValidValue(
			/*[out,retval]*/ VARIANT_BOOL * ValidValue) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Expressions * * Expressions) = 0;
	};

	struct __declspec(uuid("338fb9a0-bae5-11d2-8ad1-00c04f79e479"))
		Debugger : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall GetExpression(
			/*[in]*/ BSTR ExpressionText,
			/*[in]*/ VARIANT_BOOL UseAutoExpandRules,
			/*[in]*/ long Timeout,
			/*[out,retval]*/ struct Expression * * Expression) = 0;
		virtual HRESULT __stdcall DetachAll() = 0;
		virtual HRESULT __stdcall StepInto(
			/*[in]*/ VARIANT_BOOL WaitForBreakOrEnd) = 0;
		virtual HRESULT __stdcall StepOver(
			/*[in]*/ VARIANT_BOOL WaitForBreakOrEnd) = 0;
		virtual HRESULT __stdcall StepOut(
			/*[in]*/ VARIANT_BOOL WaitForBreakOrEnd) = 0;
		virtual HRESULT __stdcall Go(
			/*[in]*/ VARIANT_BOOL WaitForBreakOrEnd) = 0;
		virtual HRESULT __stdcall Break(
			/*[in]*/ VARIANT_BOOL WaitForBreakMode) = 0;
		virtual HRESULT __stdcall Stop(
			/*[in]*/ VARIANT_BOOL WaitForDesignMode) = 0;
		virtual HRESULT __stdcall SetNextStatement() = 0;
		virtual HRESULT __stdcall RunToCursor(
			/*[in]*/ VARIANT_BOOL WaitForBreakOrEnd) = 0;
		virtual HRESULT __stdcall ExecuteStatement(
			/*[in]*/ BSTR Statement,
			/*[in]*/ long Timeout,
			/*[in]*/ VARIANT_BOOL TreatAsExpression) = 0;
		virtual HRESULT __stdcall get_Breakpoints(
			/*[out,retval]*/ struct Breakpoints * * Breakpoints) = 0;
		virtual HRESULT __stdcall get_Languages(
			/*[out,retval]*/ struct Languages * * Languages) = 0;
		virtual HRESULT __stdcall get_CurrentMode(
			/*[out,retval]*/ enum dbgDebugMode * Mode) = 0;
		virtual HRESULT __stdcall get_CurrentProcess(
			/*[out,retval]*/ struct Process * * Process) = 0;
		virtual HRESULT __stdcall put_CurrentProcess(
			/*[in]*/ struct Process * Process) = 0;
		virtual HRESULT __stdcall get_CurrentProgram(
			/*[out,retval]*/ struct Program * * Program) = 0;
		virtual HRESULT __stdcall put_CurrentProgram(
			/*[in]*/ struct Program * Program) = 0;
		virtual HRESULT __stdcall get_CurrentThread(
			/*[out,retval]*/ struct Thread * * Thread) = 0;
		virtual HRESULT __stdcall put_CurrentThread(
			/*[in]*/ struct Thread * Thread) = 0;
		virtual HRESULT __stdcall get_CurrentStackFrame(
			/*[out,retval]*/ struct StackFrame * * StackFrame) = 0;
		virtual HRESULT __stdcall put_CurrentStackFrame(
			/*[in]*/ struct StackFrame * StackFrame) = 0;
		virtual HRESULT __stdcall get_HexDisplayMode(
			/*[out,retval]*/ VARIANT_BOOL * HexModeOn) = 0;
		virtual HRESULT __stdcall put_HexDisplayMode(
			/*[in]*/ VARIANT_BOOL HexModeOn) = 0;
		virtual HRESULT __stdcall get_HexInputMode(
			/*[out,retval]*/ VARIANT_BOOL * HexModeOn) = 0;
		virtual HRESULT __stdcall put_HexInputMode(
			/*[in]*/ VARIANT_BOOL HexModeOn) = 0;
		virtual HRESULT __stdcall get_LastBreakReason(
			/*[out,retval]*/ enum dbgEventReason * Reason) = 0;
		virtual HRESULT __stdcall get_BreakpointLastHit(
			/*[out,retval]*/ struct Breakpoint * * Breakpoint) = 0;
		virtual HRESULT __stdcall get_AllBreakpointsLastHit(
			/*[out,retval]*/ struct Breakpoints * * Breakpoints) = 0;
		virtual HRESULT __stdcall get_DebuggedProcesses(
			/*[out,retval]*/ struct Processes * * Processes) = 0;
		virtual HRESULT __stdcall get_LocalProcesses(
			/*[out,retval]*/ struct Processes * * Processes) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall TerminateAll() = 0;
	};

	struct __declspec(uuid("9f379969-5eac-4a54-b2bc-6946cffb56ef"))
		Processes : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Process * * Process) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
	};

	struct __declspec(uuid("25968106-bafb-11d2-8ad1-00c04f79e479"))
		Breakpoints : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Breakpoint * * Breakpoint) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Function,
			/*[in]*/ BSTR File,
			/*[in]*/ long Line,
			/*[in]*/ long Column,
			/*[in]*/ BSTR Condition,
			/*[in]*/ enum dbgBreakpointConditionType ConditionType,
			/*[in]*/ BSTR Language,
			/*[in]*/ BSTR Data,
			/*[in]*/ long DataCount,
			/*[in]*/ BSTR Address,
			/*[in]*/ long HitCount,
			/*[in]*/ enum dbgHitCountType HitCountType,
			/*[out,retval]*/ struct Breakpoints * * Breakpoints) = 0;
	};

	struct __declspec(uuid("11c5114c-bb00-11d2-8ad1-00c04f79e479"))
		Breakpoint : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Delete() = 0;
		virtual HRESULT __stdcall get_Type(
			/*[out,retval]*/ enum dbgBreakpointType * Type) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * Name) = 0;
		virtual HRESULT __stdcall put_Name(
			/*[in]*/ BSTR Name) = 0;
		virtual HRESULT __stdcall get_LocationType(
			/*[out,retval]*/ enum dbgBreakpointLocationType * LocationType) = 0;
		virtual HRESULT __stdcall get_FunctionName(
			/*[out,retval]*/ BSTR * FunctionName) = 0;
		virtual HRESULT __stdcall get_FunctionLineOffset(
			/*[out,retval]*/ long * Offset) = 0;
		virtual HRESULT __stdcall get_FunctionColumnOffset(
			/*[out,retval]*/ long * Offset) = 0;
		virtual HRESULT __stdcall get_File(
			/*[out,retval]*/ BSTR * File) = 0;
		virtual HRESULT __stdcall get_FileLine(
			/*[out,retval]*/ long * Line) = 0;
		virtual HRESULT __stdcall get_FileColumn(
			/*[out,retval]*/ long * Column) = 0;
		virtual HRESULT __stdcall get_ConditionType(
			/*[out,retval]*/ enum dbgBreakpointConditionType * ConditionType) = 0;
		virtual HRESULT __stdcall get_Condition(
			/*[out,retval]*/ BSTR * Condition) = 0;
		virtual HRESULT __stdcall get_Language(
			/*[out,retval]*/ BSTR * Language) = 0;
		virtual HRESULT __stdcall get_HitCountType(
			/*[out,retval]*/ enum dbgHitCountType * HitCountType) = 0;
		virtual HRESULT __stdcall get_HitCountTarget(
			/*[out,retval]*/ long * HitCountTarget) = 0;
		virtual HRESULT __stdcall get_Enabled(
			/*[out,retval]*/ VARIANT_BOOL * Enabled) = 0;
		virtual HRESULT __stdcall put_Enabled(
			/*[in]*/ VARIANT_BOOL Enabled) = 0;
		virtual HRESULT __stdcall get_Tag(
			/*[out,retval]*/ BSTR * Tag) = 0;
		virtual HRESULT __stdcall put_Tag(
			/*[in]*/ BSTR Tag) = 0;
		virtual HRESULT __stdcall get_CurrentHits(
			/*[out,retval]*/ long * CurHitCount) = 0;
		virtual HRESULT __stdcall get_Program(
			/*[out,retval]*/ struct Program * * Program) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Breakpoint * * Breakpoint) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Breakpoints * * Breakpoints) = 0;
		virtual HRESULT __stdcall get_Children(
			/*[out,retval]*/ struct Breakpoints * * Breakpoints) = 0;
		virtual HRESULT __stdcall ResetHitCount() = 0;
	};

	struct __declspec(uuid("a4f4246c-c131-11d2-8ad1-00c04f79e479"))
		Languages : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Language * * Language) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTEObject) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * Count) = 0;
	};

	struct __declspec(uuid("b3ccfa68-c145-11d2-8ad1-00c04f79e479"))
		Language : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * Name) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * DTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Debugger * * Debugger) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Languages * * Languages) = 0;
	};

	struct __declspec(uuid("26f6cc4b-7a48-4e4d-8af5-9e960232e05f"))
		_Solution : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct Project * * lppcReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall get_FileName(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall SaveAs(
			/*[in]*/ BSTR FileName) = 0;
		virtual HRESULT __stdcall AddFromTemplate(
			/*[in]*/ BSTR FileName,
			/*[in]*/ BSTR Destination,
			/*[in]*/ BSTR ProjectName,
			/*[in]*/ VARIANT_BOOL Exclusive,
			/*[out,retval]*/ struct Project * * IppptReturn) = 0;
		virtual HRESULT __stdcall AddFromFile(
			/*[in]*/ BSTR FileName,
			/*[in]*/ VARIANT_BOOL Exclusive,
			/*[out,retval]*/ struct Project * * IppptReturn) = 0;
		virtual HRESULT __stdcall Open(
			/*[in]*/ BSTR FileName) = 0;
		virtual HRESULT __stdcall Close(
			/*[in]*/ VARIANT_BOOL SaveFirst) = 0;
		virtual HRESULT __stdcall get_Properties(
			/*[out,retval]*/ struct Properties * * ppObject) = 0;
		virtual HRESULT __stdcall get_IsDirty(
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall put_IsDirty(
			/*[in]*/ VARIANT_BOOL lpfReturn) = 0;
		virtual HRESULT __stdcall Remove(
			/*[in]*/ struct Project * proj) = 0;
		virtual HRESULT __stdcall get_TemplatePath(
			/*[in]*/ BSTR ProjectType,
			/*[out,retval]*/ BSTR * lpbstrPath) = 0;
		virtual HRESULT __stdcall get_FullName(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_Saved(
			/*[out,retval]*/ VARIANT_BOOL * lpfReturn) = 0;
		virtual HRESULT __stdcall put_Saved(
			/*[in]*/ VARIANT_BOOL lpfReturn) = 0;
		virtual HRESULT __stdcall get_Globals(
			/*[out,retval]*/ struct Globals * * ppGlobals) = 0;
		virtual HRESULT __stdcall get_AddIns(
			/*[out,retval]*/ struct AddIns * * ppAddIns) = 0;
		virtual HRESULT __stdcall get_Extender(
			/*[in]*/ BSTR ExtenderName,
			/*[out,retval]*/ IDispatch * * Extender) = 0;
		virtual HRESULT __stdcall get_ExtenderNames(
			/*[out,retval]*/ VARIANT * ExtenderNames) = 0;
		virtual HRESULT __stdcall get_ExtenderCATID(
			/*[out,retval]*/ BSTR * pRetval) = 0;
		virtual HRESULT __stdcall get_IsOpen(
			/*[out,retval]*/ VARIANT_BOOL * pVal) = 0;
		virtual HRESULT __stdcall get_SolutionBuild(
			/*[out,retval]*/ struct SolutionBuild * * ppSolutionBuild) = 0;
		virtual HRESULT __stdcall Create(
			BSTR Destination,
			BSTR Name) = 0;
		virtual HRESULT __stdcall get_Projects(
			/*[out,retval]*/ struct Projects * * ppProjects) = 0;
		virtual HRESULT __stdcall FindProjectItem(
			BSTR FileName,
			/*[out,retval]*/ struct ProjectItem * * ppProjectItem) = 0;
		virtual HRESULT __stdcall ProjectItemsTemplatePath(
			BSTR ProjectKind,
			/*[out,retval]*/ BSTR * pFullPath) = 0;
	};

	struct __declspec(uuid("a3c1c40c-9218-4d4c-9daa-075f64f6922c"))
		SolutionBuild : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _Solution * * ppSolution) = 0;
		virtual HRESULT __stdcall get_ActiveConfiguration(
			/*[out,retval]*/ struct SolutionConfiguration * * ppSolutionConfiguration) = 0;
		virtual HRESULT __stdcall get_BuildDependencies(
			/*[out,retval]*/ struct BuildDependencies * * ppBuildDependencies) = 0;
		virtual HRESULT __stdcall get_BuildState(
			/*[out,retval]*/ enum vsBuildState * pvsBuildState) = 0;
		virtual HRESULT __stdcall get_LastBuildInfo(
			/*[out,retval]*/ long * pBuiltSuccessfully) = 0;
		virtual HRESULT __stdcall put_StartupProjects(
			/*[in]*/ VARIANT pProject) = 0;
		virtual HRESULT __stdcall get_StartupProjects(
			/*[out,retval]*/ VARIANT * pProject) = 0;
		virtual HRESULT __stdcall Build(
			/*[in]*/ VARIANT_BOOL WaitForBuildToFinish) = 0;
		virtual HRESULT __stdcall Debug() = 0;
		virtual HRESULT __stdcall Deploy(
			/*[in]*/ VARIANT_BOOL WaitForDeployToFinish) = 0;
		virtual HRESULT __stdcall Clean(
			/*[in]*/ VARIANT_BOOL WaitForCleanToFinish) = 0;
		virtual HRESULT __stdcall Run() = 0;
		virtual HRESULT __stdcall get_SolutionConfigurations(
			/*[out,retval]*/ struct SolutionConfigurations * * ppSolutionConfigurations) = 0;
		virtual HRESULT __stdcall BuildProject(
			BSTR SolutionConfiguration,
			BSTR ProjectUniqueName,
			VARIANT_BOOL WaitForBuildToFinish) = 0;
	};

	struct __declspec(uuid("60aaad75-cb8d-4c62-9959-24d6a6a50de7"))
		SolutionConfiguration : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct SolutionConfigurations * * ppSolutionConfigurations) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pVal) = 0;
		virtual HRESULT __stdcall get_SolutionContexts(
			/*[out,retval]*/ struct SolutionContexts * * ppOut) = 0;
		virtual HRESULT __stdcall Delete() = 0;
		virtual HRESULT __stdcall Activate() = 0;
	};

	struct __declspec(uuid("23e78ed7-c9e1-462d-8bc6-366003486ed9"))
		SolutionConfigurations : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct SolutionConfiguration * * ppOut) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct SolutionBuild * * ppBuild) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR NewName,
			/*[in]*/ BSTR ExistingName,
			/*[in]*/ VARIANT_BOOL Propagate,
			/*[out,retval]*/ struct SolutionConfiguration * * ppSolutionConfiguration) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
	};

	struct __declspec(uuid("0685b546-fb84-4917-ab98-98d40f892d61"))
		SolutionContexts : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct SolutionConfiguration * * ppSolutionConfiguration) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct SolutionContext * * ppOut) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
	};

	struct __declspec(uuid("fc6a1a82-9c8a-47bb-a046-6e965df5a99b"))
		SolutionContext : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct SolutionContexts * * ppSolutionContexts) = 0;
		virtual HRESULT __stdcall get_ProjectName(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_ConfigurationName(
			/*[out,retval]*/ BSTR * pConfigurationName) = 0;
		virtual HRESULT __stdcall put_ConfigurationName(
			/*[in]*/ BSTR pConfigurationName) = 0;
		virtual HRESULT __stdcall get_PlatformName(
			/*[out,retval]*/ BSTR * pPlatformName) = 0;
		virtual HRESULT __stdcall get_ShouldBuild(
			/*[out,retval]*/ VARIANT_BOOL * pPlatformName) = 0;
		virtual HRESULT __stdcall put_ShouldBuild(
			/*[in]*/ VARIANT_BOOL pPlatformName) = 0;
		virtual HRESULT __stdcall get_ShouldDeploy(
			/*[out,retval]*/ VARIANT_BOOL * pPlatformName) = 0;
		virtual HRESULT __stdcall put_ShouldDeploy(
			/*[in]*/ VARIANT_BOOL pPlatformName) = 0;
	};

	struct __declspec(uuid("ead260eb-1e5b-450a-b628-4cfada11b4a1"))
		BuildDependencies : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct SolutionBuild * * ppSolutionBuild) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * Enumerator) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct BuildDependency * * ppOut) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
	};

	struct __declspec(uuid("9c5ceaac-062f-4434-a2ed-78ab4d6134fe"))
		BuildDependency : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct BuildDependencies * * ppBuildDependencies) = 0;
		virtual HRESULT __stdcall get_Project(
			/*[out,retval]*/ struct Project * * ppProject) = 0;
		virtual HRESULT __stdcall get_RequiredProjects(
			/*[out,retval]*/ VARIANT * pProjects) = 0;
		virtual HRESULT __stdcall AddProject(
			BSTR ProjectUniqueName) = 0;
		virtual HRESULT __stdcall RemoveProject(
			BSTR ProjectUniqueName) = 0;
		virtual HRESULT __stdcall RemoveAllProjects() = 0;
	};

	struct __declspec(uuid("e6b96cac-b8c7-40ae-b705-5c81878c4a9e"))
		Commands : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Guid,
			/*[in]*/ long ID,
			/*[in]*/ VARIANT * Control) = 0;
		virtual HRESULT __stdcall Raise(
			/*[in]*/ BSTR Guid,
			/*[in]*/ long ID,
			/*[in,out]*/ VARIANT * CustomIn,
			/*[in,out]*/ VARIANT * CustomOut) = 0;
		virtual HRESULT __stdcall CommandInfo(
			/*[in]*/ IDispatch * CommandBarControl,
			/*[out]*/ BSTR * Guid,
			/*[out]*/ long * ID) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[in]*/ long ID,
			/*[out,retval]*/ struct Command * * lppcReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall AddNamedCommand(
			/*[in]*/ struct AddIn * AddInInstance,
			/*[in]*/ BSTR Name,
			/*[in]*/ BSTR ButtonText,
			/*[in]*/ BSTR Tooltip,
			/*[in]*/ VARIANT_BOOL MSOButton,
			/*[in]*/ long Bitmap,
			/*[in]*/ SAFEARRAY * * ContextUIGUIDs,
			/*[in]*/ long vsCommandDisabledFlagsValue,
			/*[out,retval]*/ struct Command * * pVal) = 0;
		virtual HRESULT __stdcall AddCommandBar(
			/*[in]*/ BSTR Name,
			/*[in]*/ enum vsCommandBarType Type,
			/*[in]*/ IDispatch * CommandBarParent,
			/*[in]*/ long Position,
			/*[out,retval]*/ IDispatch * * pVal) = 0;
		virtual HRESULT __stdcall RemoveCommandBar(
			/*[in]*/ IDispatch * CommandBar) = 0;
	};

	struct __declspec(uuid("5fe10fb0-91a1-4e55-baaa-eccae5cceb94"))
		Command : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * lpbstr) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct Commands * * lppcReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_Guid(
			/*[out,retval]*/ BSTR * lpbstr) = 0;
		virtual HRESULT __stdcall get_ID(
			/*[out,retval]*/ long * lReturn) = 0;
		virtual HRESULT __stdcall get_IsAvailable(
			/*[out,retval]*/ VARIANT_BOOL * pAvail) = 0;
		virtual HRESULT __stdcall AddControl(
			/*[in]*/ IDispatch * Owner,
			/*[in]*/ long Position,
			/*[out,retval]*/ IDispatch * * pCommandBarControl) = 0;
		virtual HRESULT __stdcall Delete() = 0;
		virtual HRESULT __stdcall get_Bindings(
			/*[out,retval]*/ VARIANT * pVar) = 0;
		virtual HRESULT __stdcall put_Bindings(
			VARIANT pVar) = 0;
		virtual HRESULT __stdcall get_LocalizedName(
			/*[out,retval]*/ BSTR * lpbstr) = 0;
	};

	struct __declspec(uuid("6caa67cf-43ae-4184-aaab-0200ddf6b240"))
		SelectedItems : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct SelectedItem * * lplppReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppaReturn) = 0;
		virtual HRESULT __stdcall get_MultiSelect(
			/*[out,retval]*/ VARIANT_BOOL * pfMultiSelect) = 0;
		virtual HRESULT __stdcall get_SelectionContainer(
			/*[out,retval]*/ struct SelectionContainer * * lppdispSelContainer) = 0;
	};

	struct __declspec(uuid("049d2cdf-3731-4cb6-a233-be97bce922d3"))
		SelectedItem : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct SelectedItems * * lppReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppReturn) = 0;
		virtual HRESULT __stdcall get_Project(
			/*[out,retval]*/ struct Project * * lppReturn) = 0;
		virtual HRESULT __stdcall get_ProjectItem(
			/*[out,retval]*/ struct ProjectItem * * lppReturn) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * lpbstrReturn) = 0;
		virtual HRESULT __stdcall get_InfoCount(
			/*[out,retval]*/ short * lpnCount) = 0;
		virtual HRESULT __stdcall get_Info(
			/*[in]*/ short index,
			/*[out,retval]*/ VARIANT * lpbstrReturn) = 0;
	};

	struct __declspec(uuid("02273422-8dd4-4a9f-8a8b-d70443d510f4"))
		SelectionContainer : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ IDispatch * * lplppReturn) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * lplReturn) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct SelectedItems * * lppReturn) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * lppReturn) = 0;
	};

	struct __declspec(uuid("e577442a-98e1-46c5-bd2e-d25807ec81ce"))
		WindowConfigurations : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * ppEnum) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct WindowConfiguration * * pWindowConfiguration) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct _DTE * * pParent) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ struct WindowConfiguration * * pWindowConfiguration) = 0;
		virtual HRESULT __stdcall get_ActiveConfigurationName(
			/*[out,retval]*/ BSTR * pbstrName) = 0;
	};

	struct __declspec(uuid("41d02413-8a67-4c28-a980-ad7539ed415d"))
		WindowConfiguration : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct WindowConfigurations * * pWindowConfigurations) = 0;
		virtual HRESULT __stdcall Apply(
			/*[in]*/ VARIANT_BOOL FromCustomViews) = 0;
		virtual HRESULT __stdcall Delete() = 0;
		virtual HRESULT __stdcall Update() = 0;
	};

	struct __declspec(uuid("56fcd5af-7f17-4c5c-aa8d-ae2bb2ddbf38"))
		ToolBox : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * pParent) = 0;
		virtual HRESULT __stdcall get_ActiveTab(
			/*[out,retval]*/ struct ToolBoxTab * * pToolBoxTab) = 0;
		virtual HRESULT __stdcall get_ToolBoxTabs(
			/*[out,retval]*/ struct ToolBoxTabs * * pToolBoxTabs) = 0;
	};

	struct __declspec(uuid("ce2def9e-3387-4bf2-967b-a1f7f70df325"))
		ToolBoxTab : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pName) = 0;
		virtual HRESULT __stdcall put_Name(
			/*[in]*/ BSTR pName) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct ToolBoxTabs * * pToolBoxTabs) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall Activate() = 0;
		virtual HRESULT __stdcall Delete() = 0;
		virtual HRESULT __stdcall get_ToolBoxItems(
			/*[out,retval]*/ struct ToolBoxItems * * pToolBoxItems) = 0;
		virtual HRESULT __stdcall get_ListView(
			/*[out,retval]*/ VARIANT_BOOL * pListView) = 0;
		virtual HRESULT __stdcall put_ListView(
			/*[in]*/ VARIANT_BOOL pListView) = 0;
	};

	struct __declspec(uuid("cf177b52-4f2f-42a0-8da3-ce78679a0d2d"))
		ToolBoxTabs : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall Item(
			VARIANT index,
			/*[out,retval]*/ struct ToolBoxTab * * pItem) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct ToolBox * * pParent) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Name,
			/*[out,retval]*/ struct ToolBoxTab * * pToolBoxTabs) = 0;
	};

	struct __declspec(uuid("395c7dfb-f158-431c-8f43-dda83b4ef54e"))
		ToolBoxItems : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * lppiuReturn) = 0;
		virtual HRESULT __stdcall Item(
			VARIANT index,
			/*[out,retval]*/ struct ToolBoxItem * * pItem) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct ToolBoxTab * * pParent) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Add(
			/*[in]*/ BSTR Name,
			/*[in]*/ VARIANT Data,
			/*[in]*/ enum vsToolBoxItemFormat Format,
			/*[out,retval]*/ struct ToolBoxItem * * pToolBoxItem) = 0;
		virtual HRESULT __stdcall get_SelectedItem(
			/*[out,retval]*/ struct ToolBoxItem * * pToolBoxItem) = 0;
	};

	struct __declspec(uuid("46538d1b-4d81-4002-8bb4-dbdb65babb23"))
		ToolBoxItem : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * Name) = 0;
		virtual HRESULT __stdcall put_Name(
			/*[in]*/ BSTR Name) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct ToolBoxItems * * pToolBoxItems) = 0;
		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * pDTE) = 0;
		virtual HRESULT __stdcall Delete() = 0;
		virtual HRESULT __stdcall Select() = 0;
	};

	struct __declspec(uuid("fbd0d024-09cd-4d9f-9e2b-cacd628426a5"))
		UIHierarchyItem : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Collection(
			/*[out,retval]*/ struct UIHierarchyItems * * ppUIHierarchyItems) = 0;
		virtual HRESULT __stdcall get_Name(
			/*[out,retval]*/ BSTR * pbstrName) = 0;
		virtual HRESULT __stdcall get_UIHierarchyItems(
			/*[out,retval]*/ struct UIHierarchyItems * * ppUIHierarchyItems) = 0;
		virtual HRESULT __stdcall get_Object(
			/*[out,retval]*/ IDispatch * * ppDispatch) = 0;
		virtual HRESULT __stdcall Select(
			/*[in]*/ enum vsUISelectionType How) = 0;
		virtual HRESULT __stdcall get_IsSelected(
			/*[out,retval]*/ VARIANT_BOOL * pfSelected) = 0;
	};

	struct __declspec(uuid("db8406b0-a916-449c-a277-bb04028f4394"))
		UIHierarchyItems : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ IDispatch * * ppDispatch) = 0;
		virtual HRESULT __stdcall get_Count(
			/*[out,retval]*/ long * pCount) = 0;
		virtual HRESULT __stdcall Item(
			/*[in]*/ VARIANT index,
			/*[out,retval]*/ struct UIHierarchyItem * * ppUIHierarchyItem) = 0;
		virtual HRESULT __stdcall get_Expanded(
			/*[out,retval]*/ VARIANT_BOOL * pfExpanded) = 0;
		virtual HRESULT __stdcall put_Expanded(
			/*[in]*/ VARIANT_BOOL pfExpanded) = 0;
		virtual HRESULT __stdcall _NewEnum(
			/*[out,retval]*/ IUnknown * * ppUnknown) = 0;
	};

	struct __declspec(uuid("72a2a2ef-c209-408c-a377-76871774adb7"))
		UIHierarchy : IDispatch
	{
		//
		// Raw methods provided by interface
		//

		virtual HRESULT __stdcall get_DTE(
			/*[out,retval]*/ struct _DTE * * ppDTE) = 0;
		virtual HRESULT __stdcall get_Parent(
			/*[out,retval]*/ struct Window * * ppWin) = 0;
		virtual HRESULT __stdcall get_UIHierarchyItems(
			/*[out,retval]*/ struct UIHierarchyItems * * ppUIHierarchyItems) = 0;
		virtual HRESULT __stdcall get_SelectedItems(
			/*[out,retval]*/ VARIANT * pvarSel) = 0;
		virtual HRESULT __stdcall GetItem(
			/*[in]*/ BSTR Names,
			/*[out,retval]*/ struct UIHierarchyItem * * ppUIHierarchyItem) = 0;
		virtual HRESULT __stdcall SelectUp(
			/*[in]*/ enum vsUISelectionType How,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall SelectDown(
			/*[in]*/ enum vsUISelectionType How,
			/*[in]*/ long Count) = 0;
		virtual HRESULT __stdcall DoDefaultAction() = 0;
	};

	//
	// Named GUID constants initializations
	//

	extern "C" const GUID __declspec(selectany) LIBID_EnvDTE =
	{ 0x80cc9f66,0xe7d8,0x4ddd,{0x85,0xb6,0xd9,0xe6,0xcd,0x0e,0x93,0xe2} };
	extern "C" const GUID __declspec(selectany) CLSID_DTE =
	{ 0x3c9cfe1e,0x389f,0x4118,{0x9f,0xad,0x36,0x53,0x85,0x19,0x03,0x29} };
	extern "C" const GUID __declspec(selectany) IID_Globals =
	{ 0xe68a3e0e,0xb435,0x4dde,{0x86,0xb7,0xf5,0xad,0xef,0xc1,0x9d,0xf2} };
	extern "C" const GUID __declspec(selectany) CLSID_CommandEvents =
	{ 0x1ded92b5,0x9a46,0x4b29,{0x93,0xef,0xb5,0xe0,0x70,0x16,0x65,0x9e} };
	extern "C" const GUID __declspec(selectany) IID__CommandEvents =
	{ 0xa79fc678,0x0d0a,0x496a,{0xb9,0xdc,0x0d,0x5b,0x9e,0x1c,0xa9,0xfc} };
	extern "C" const GUID __declspec(selectany) DIID__dispCommandEvents =
	{ 0xff2d5c12,0xfeea,0x11d0,{0xbb,0xc8,0x00,0xa0,0xc9,0x0f,0x27,0x44} };
	extern "C" const GUID __declspec(selectany) CLSID_SelectionEvents =
	{ 0xaf37511e,0x9e9d,0x4234,{0xa5,0xa1,0x75,0x84,0xd2,0x90,0xe0,0x61} };
	extern "C" const GUID __declspec(selectany) IID__SelectionEvents =
	{ 0xeb6783db,0x1819,0x496d,{0x84,0xa4,0x3c,0xff,0x88,0x30,0x10,0xf6} };
	extern "C" const GUID __declspec(selectany) DIID__dispSelectionEvents =
	{ 0xdeeb28b3,0x23e6,0x11d1,{0xae,0x5c,0x00,0xa0,0xc9,0x0f,0x26,0xf4} };
	extern "C" const GUID __declspec(selectany) CLSID_SolutionEvents =
	{ 0x88ac98c7,0xb38c,0x404b,{0xbd,0x86,0xd2,0xa4,0xf2,0xe8,0x9d,0xca} };
	extern "C" const GUID __declspec(selectany) IID__SolutionEvents =
	{ 0xbf8bbf37,0x5415,0x46a9,{0x94,0x0d,0x59,0x4c,0xad,0x9d,0xec,0x26} };
	extern "C" const GUID __declspec(selectany) DIID__dispSolutionEvents =
	{ 0xfbcff1c2,0x261c,0x11d1,{0xae,0x5e,0x00,0xa0,0xc9,0x0f,0x26,0xf4} };
	extern "C" const GUID __declspec(selectany) CLSID_BuildEvents =
	{ 0xd83d60e3,0x229f,0x4660,{0x8d,0xd0,0x28,0xb6,0x29,0xee,0xdc,0xda} };
	extern "C" const GUID __declspec(selectany) IID__BuildEvents =
	{ 0x794a2ba5,0xffa6,0x4fc5,{0xbf,0x13,0x95,0x7b,0x2c,0x22,0xed,0xd7} };
	extern "C" const GUID __declspec(selectany) DIID__dispBuildEvents =
	{ 0x1926364e,0x6b90,0x46cb,{0xa4,0x4d,0x8a,0x80,0xfb,0x11,0xac,0xd9} };
	extern "C" const GUID __declspec(selectany) CLSID_WindowEvents =
	{ 0x2e260fd4,0xc130,0x4e6d,{0x8e,0xbc,0x4a,0x3b,0xfd,0x18,0x81,0x81} };
	extern "C" const GUID __declspec(selectany) IID__WindowEvents =
	{ 0x0d3a23a8,0x67bb,0x11d2,{0x97,0xc1,0x00,0xc0,0x4f,0xb6,0xc6,0xff} };
	extern "C" const GUID __declspec(selectany) DIID__dispWindowEvents =
	{ 0x0d3a23a9,0x67bb,0x11d2,{0x97,0xc1,0x00,0xc0,0x4f,0xb6,0xc6,0xff} };
	extern "C" const GUID __declspec(selectany) CLSID_OutputWindowEvents =
	{ 0x3760037f,0xb012,0x44f8,{0x9c,0x23,0x36,0x09,0xd7,0xa1,0x6d,0xef} };
	extern "C" const GUID __declspec(selectany) IID__OutputWindowEvents =
	{ 0x0a3546a8,0x6840,0x11d2,{0x97,0xc1,0x00,0xc0,0x4f,0xb6,0xc6,0xff} };
	extern "C" const GUID __declspec(selectany) DIID__dispOutputWindowEvents =
	{ 0x0d3a23af,0x67bb,0x11d2,{0x97,0xc1,0x00,0xc0,0x4f,0xb6,0xc6,0xff} };
	extern "C" const GUID __declspec(selectany) CLSID_FindEvents =
	{ 0x811322bc,0x042d,0x4828,{0xbf,0xf2,0x64,0x0e,0xf8,0xb7,0x20,0x9f} };
	extern "C" const GUID __declspec(selectany) IID__FindEvents =
	{ 0xc5331acd,0xc5d5,0x11d2,{0x85,0x98,0x00,0x60,0x97,0xc6,0x8e,0x81} };
	extern "C" const GUID __declspec(selectany) DIID__dispFindEvents =
	{ 0xc5331ace,0xc5d5,0x11d2,{0x85,0x98,0x00,0x60,0x97,0xc6,0x8e,0x81} };
	extern "C" const GUID __declspec(selectany) CLSID_TaskListEvents =
	{ 0x29617acd,0x7859,0x4328,{0xbe,0x09,0x29,0x8f,0x91,0xf4,0x81,0x96} };
	extern "C" const GUID __declspec(selectany) IID__TaskListEvents =
	{ 0x1125c422,0x49bd,0x11d2,{0x88,0x23,0x00,0xc0,0x4f,0xb6,0xc6,0xff} };
	extern "C" const GUID __declspec(selectany) DIID__dispTaskListEvents =
	{ 0x1125c423,0x49bd,0x11d2,{0x88,0x23,0x00,0xc0,0x4f,0xb6,0xc6,0xff} };
	extern "C" const GUID __declspec(selectany) CLSID_DTEEvents =
	{ 0xc6304bab,0x6765,0x4c63,{0x90,0x17,0x49,0x40,0xae,0xb6,0xf2,0x07} };
	extern "C" const GUID __declspec(selectany) IID__DTEEvents =
	{ 0xfa1bb6d7,0xca83,0x11d2,{0xaa,0xb2,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) DIID__dispDTEEvents =
	{ 0xb50c9708,0xc909,0x4b87,{0xa0,0x3d,0xaf,0x6c,0xc4,0xbf,0xb4,0x22} };
	extern "C" const GUID __declspec(selectany) CLSID_DocumentEvents =
	{ 0xdc5437f7,0xf114,0x11d2,{0xaa,0xcf,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID__DocumentEvents =
	{ 0xdc5437f5,0xf114,0x11d2,{0xaa,0xcf,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) DIID__dispDocumentEvents =
	{ 0xdc5437f6,0xf114,0x11d2,{0xaa,0xcf,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) CLSID_ProjectItemsEvents =
	{ 0xde6c1098,0x93ca,0x4f49,{0xbe,0xf0,0x26,0x2a,0x13,0xca,0x11,0x76} };
	extern "C" const GUID __declspec(selectany) IID__ProjectItemsEvents =
	{ 0x22800963,0x2811,0x410d,{0xbf,0x87,0xa7,0x80,0x8e,0xac,0x97,0x7d} };
	extern "C" const GUID __declspec(selectany) DIID__dispProjectItemsEvents =
	{ 0x6962753f,0xefd5,0x41c5,{0xb0,0x83,0xd7,0x06,0x87,0x16,0x6a,0xeb} };
	extern "C" const GUID __declspec(selectany) CLSID_DebuggerEvents =
	{ 0x0c763210,0x0fbb,0x11d3,{0xb8,0x80,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID__DebuggerEvents =
	{ 0xd4eae958,0x0fba,0x11d3,{0xb8,0x80,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) DIID__dispDebuggerEvents =
	{ 0x46209330,0x0fba,0x11d3,{0xb8,0x80,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) CLSID_TextEditorEvents =
	{ 0xadf22c37,0x0069,0x4adf,{0xb1,0x2d,0xd8,0xd4,0x7c,0x38,0xfe,0x79} };
	extern "C" const GUID __declspec(selectany) IID__TextEditorEvents =
	{ 0x23b7a868,0x6c89,0x436a,{0x94,0xfa,0x25,0xd7,0x55,0x45,0x6a,0x77} };
	extern "C" const GUID __declspec(selectany) DIID__dispTextEditorEvents =
	{ 0x2699dd44,0xc507,0x4da3,{0xaa,0x34,0x31,0x4a,0x6c,0x21,0xdf,0xe2} };
	extern "C" const GUID __declspec(selectany) CLSID_Solution =
	{ 0xb35caa8c,0x77de,0x4ab3,{0x8e,0x5a,0xf0,0x38,0xe3,0xfc,0x60,0x56} };
	extern "C" const GUID __declspec(selectany) IID_StatusBar =
	{ 0xc34301a1,0x3ef1,0x41d8,{0x93,0x2a,0xfe,0xa4,0xa8,0xa8,0xce,0x0c} };
	extern "C" const GUID __declspec(selectany) IID_IExtenderSite =
	{ 0xe57c510b,0x968b,0x4a3c,{0xa4,0x67,0xee,0x40,0x13,0x15,0x7d,0xc9} };
	extern "C" const GUID __declspec(selectany) IID_IExtenderProvider =
	{ 0x4db06329,0x23f4,0x443b,{0x9a,0xbd,0x9c,0xf6,0x11,0xe8,0xae,0x07} };
	extern "C" const GUID __declspec(selectany) IID_IExtenderProviderUnk =
	{ 0xf69b64a3,0x9017,0x4e48,{0x97,0x84,0xe1,0x52,0xb5,0x1a,0xa7,0x22} };
	extern "C" const GUID __declspec(selectany) IID_ObjectExtenders =
	{ 0x8d0aa9cc,0x8465,0x42f3,{0xad,0x6e,0xdf,0xde,0x28,0xcc,0xc7,0x5d} };
	extern "C" const GUID __declspec(selectany) IID_Find =
	{ 0x40d4b9b6,0x739b,0x4965,{0x8d,0x65,0x69,0x2a,0xec,0x69,0x22,0x66} };
	extern "C" const GUID __declspec(selectany) IID_UndoContext =
	{ 0xd8dec44d,0xcaf2,0x4b39,{0xa5,0x39,0xb9,0x1a,0xe9,0x21,0xba,0x92} };
	extern "C" const GUID __declspec(selectany) IID_Macros =
	{ 0xf9f99155,0x6d4d,0x49b1,{0xad,0x63,0xc7,0x8c,0x3e,0x8a,0x59,0x16} };
	extern "C" const GUID __declspec(selectany) IID_SourceControl =
	{ 0xf1ddc2c2,0xdf76,0x4ebb,{0x9d,0xe8,0x48,0xad,0x25,0x57,0x06,0x2c} };
	extern "C" const GUID __declspec(selectany) IID_ISupportVSProperties =
	{ 0x6659ed14,0x2ab6,0x47f3,{0xa8,0x90,0x00,0xc8,0xab,0xa4,0x3b,0x84} };
	extern "C" const GUID __declspec(selectany) IID_IExtensibleObjectSite =
	{ 0xeb5be8a7,0xe593,0x4de6,{0xa9,0x23,0xc2,0xaf,0xec,0xb9,0x63,0x36} };
	extern "C" const GUID __declspec(selectany) IID_IVsProfferCommands =
	{ 0x8cc0cde1,0xc16a,0x4749,{0x99,0xaf,0x6f,0x75,0x23,0xc3,0x4a,0x57} };
	extern "C" const GUID __declspec(selectany) IID_IFilterProperties =
	{ 0xaade1f59,0x6ace,0x43d1,{0x8f,0xca,0x42,0xaf,0x3a,0x5c,0x4f,0x3c} };
	extern "C" const GUID __declspec(selectany) IID_TextEditor =
	{ 0x9ff3ddca,0x1795,0x4191,{0xa5,0xb1,0x02,0xd1,0xae,0x35,0xd0,0x74} };
	extern "C" const GUID __declspec(selectany) IID__DebuggerEventsRoot =
	{ 0xd4bb39fb,0x0f0e,0x11d3,{0xb8,0x80,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_IDTWizard =
	{ 0xe914bbe1,0x03a4,0x11d1,{0xbb,0xcd,0x00,0xa0,0xc9,0x0f,0x27,0x44} };
	extern "C" const GUID __declspec(selectany) IID_IVsGlobalsCallback =
	{ 0xe2cc506a,0x588b,0x4f65,{0xa1,0xf0,0x22,0x44,0xc0,0x60,0xab,0xcb} };
	extern "C" const GUID __declspec(selectany) IID_IVsGlobals =
	{ 0x192ac688,0xe7c6,0x4f9d,{0x81,0x80,0x4b,0x37,0xef,0xbf,0x6f,0x3a} };
	extern "C" const GUID __declspec(selectany) IID_IDTCommandTarget =
	{ 0x7ef39a3e,0x590d,0x4879,{0x88,0xd4,0xc9,0xbe,0x5b,0xcf,0xd9,0x2e} };
	extern "C" const GUID __declspec(selectany) IID__ProjectsEvents =
	{ 0x85451f83,0xb5ca,0x437f,{0xa6,0x19,0x0c,0xb7,0x05,0x70,0x74,0x20} };
	extern "C" const GUID __declspec(selectany) DIID__dispProjectsEvents =
	{ 0x7f508d55,0x627f,0x4d7f,{0xbe,0x0b,0x9e,0x3d,0x82,0x9f,0xf0,0xed} };
	extern "C" const GUID __declspec(selectany) CLSID_ProjectsEvents =
	{ 0x536a4be3,0xa376,0x408e,{0x95,0x4c,0x47,0x1c,0x77,0x9e,0x21,0x6f} };
	extern "C" const GUID __declspec(selectany) IID__MiscSlnFilesEventsRoot =
	{ 0x7658b944,0xf37b,0x11d2,{0xaa,0xcf,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID__CommandBarControlEvents =
	{ 0x9e66fe98,0xa1c6,0x421d,{0x8c,0x0c,0x6d,0xa4,0xe6,0x52,0xe7,0x70} };
	extern "C" const GUID __declspec(selectany) DIID__dispCommandBarControlEvents =
	{ 0x987fb893,0xf96d,0x11d0,{0xbb,0xbb,0x00,0xa0,0xc9,0x0f,0x27,0x44} };
	extern "C" const GUID __declspec(selectany) CLSID_CommandBarEvents =
	{ 0xbfd4b2b2,0x9eec,0x4db8,{0xab,0xa0,0xac,0x31,0x6f,0x4c,0x73,0x28} };
	extern "C" const GUID __declspec(selectany) IID__OutputWindowEventsRoot =
	{ 0xaa6f4085,0x33b6,0x4629,{0xb9,0xea,0x69,0x21,0x01,0x00,0x7c,0xc2} };
	extern "C" const GUID __declspec(selectany) IID__TaskListEventsRoot =
	{ 0x6bc8c372,0xc6f0,0x4be6,{0xb2,0x55,0x82,0x7a,0xc1,0x90,0xbf,0x71} };
	extern "C" const GUID __declspec(selectany) IID_IDTToolsOptionsPage =
	{ 0xbdcaf240,0x2692,0x4713,{0x90,0x2a,0xb1,0x10,0xb1,0xd0,0xf1,0x00} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentWebBrowser =
	{ 0xa3286b03,0x5ac6,0x44f0,{0x8c,0xc3,0xeb,0xed,0x7f,0x11,0x24,0xe5} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentHelp =
	{ 0xaebded64,0xa206,0x11d3,{0xb8,0xb5,0x00,0xc0,0x4f,0x79,0xf8,0x02} };
	extern "C" const GUID __declspec(selectany) IID_ColorableItems =
	{ 0xe5d17051,0xd6e5,0x4da7,{0x8b,0x3a,0xca,0x88,0x86,0x17,0xa5,0xe7} };
	extern "C" const GUID __declspec(selectany) IID_FontsAndColorsItems =
	{ 0xf25ae7e6,0x1460,0x4ba4,{0x8e,0x5e,0xbb,0xbe,0x74,0x6d,0xe3,0x53} };
	extern "C" const GUID __declspec(selectany) IID__FontsAndColors =
	{ 0x256068f6,0x1add,0x4f7b,{0xba,0x76,0x57,0x13,0x14,0xc4,0x13,0xad} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentGeneral =
	{ 0x48e61d9c,0x8c8d,0x42d3,{0x91,0x4b,0x46,0xd7,0x0c,0x8b,0x7a,0x40} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentFontsAndColors =
	{ 0xf809cab6,0x2c9f,0x41f2,{0xa5,0xaf,0xe2,0x6f,0xb8,0x0e,0x62,0xad} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentProjectsAndSolution =
	{ 0x478f06d4,0x5d57,0x473f,{0x9b,0x74,0x5f,0x8e,0x88,0xef,0xa5,0xe7} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentDocuments =
	{ 0x76ed1c48,0xed86,0x4e9e,{0xac,0xf8,0xa4,0x0e,0x76,0x5d,0xaf,0x25} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentTaskList =
	{ 0x4bc18a5b,0xdbb6,0x4af5,{0xa4,0x43,0x2e,0x3f,0x19,0x36,0x53,0x04} };
	extern "C" const GUID __declspec(selectany) IID__EnvironmentKeyboard =
	{ 0x9c722678,0x490d,0x408f,{0x98,0xae,0xb6,0xb9,0xa6,0x8a,0xa4,0x5d} };
	extern "C" const GUID __declspec(selectany) IID_IExtensibleObject =
	{ 0x86c31347,0x5b52,0x4715,{0xb4,0x54,0xa6,0xe5,0xfc,0xab,0x97,0x5d} };
	extern "C" const GUID __declspec(selectany) IID_IVsTextEditGeneral =
	{ 0x2e1bfd1c,0x5b26,0x4aca,{0xb9,0x7b,0xed,0x9d,0x26,0x1b,0xa3,0xe7} };
	extern "C" const GUID __declspec(selectany) IID_IVsTextEditPerLanguage =
	{ 0x5943bd7e,0xd722,0x42db,{0xa2,0x51,0xfe,0x2a,0xdd,0x87,0x11,0xea} };
	extern "C" const GUID __declspec(selectany) IID_IVsTextEditFonts =
	{ 0xf39ab913,0xe6c9,0x4546,{0xa2,0x65,0x1e,0x43,0xf8,0xde,0x92,0x4c} };
	extern "C" const GUID __declspec(selectany) IID__DTE =
	{ 0x04a72314,0x32e9,0x48e2,{0x9b,0x87,0xa6,0x36,0x03,0x45,0x4f,0x3e} };
	extern "C" const GUID __declspec(selectany) IID_Windows =
	{ 0x2294311a,0xb7bc,0x4789,{0xb3,0x65,0x1c,0x15,0xff,0x2c,0xd1,0x7c} };
	extern "C" const GUID __declspec(selectany) IID_Window =
	{ 0x0beab46b,0x4c07,0x4f94,{0xa8,0xd7,0x16,0x26,0x02,0x0e,0x4e,0x53} };
	extern "C" const GUID __declspec(selectany) IID_LinkedWindows =
	{ 0xf00ef34a,0xa654,0x4c1b,{0x89,0x7a,0x58,0x5d,0x5b,0xcb,0xb3,0x5a} };
	extern "C" const GUID __declspec(selectany) IID_TextBuffer =
	{ 0xf47dc7e7,0x84b6,0x474f,{0xbb,0x91,0x63,0x16,0x40,0xaa,0x05,0x60} };
	extern "C" const GUID __declspec(selectany) IID__WindowEventsRoot =
	{ 0x17d12026,0xba99,0x403e,{0xa3,0x59,0x71,0xfd,0x1e,0x5a,0x72,0xcd} };
	extern "C" const GUID __declspec(selectany) IID_HTMLWindow =
	{ 0xf6576203,0xfbce,0x477e,{0xa6,0x6b,0xed,0xa2,0x37,0xbb,0x68,0xa7} };
	extern "C" const GUID __declspec(selectany) IID_ProjectItem =
	{ 0x0b48100a,0x473e,0x433c,{0xab,0x8f,0x66,0xb9,0x73,0x9a,0xb6,0x20} };
	extern "C" const GUID __declspec(selectany) IID_ItemOperations =
	{ 0xd5dbe57b,0xc074,0x4e95,{0xb0,0x15,0xab,0xee,0xaa,0x39,0x16,0x93} };
	extern "C" const GUID __declspec(selectany) IID_ProjectItems =
	{ 0x8e2f1269,0x185e,0x43c7,{0x88,0x99,0x95,0x0a,0xd2,0x76,0x9c,0xcf} };
	extern "C" const GUID __declspec(selectany) IID_Project =
	{ 0x866311e6,0xc887,0x4143,{0x98,0x33,0x64,0x5f,0x5b,0x93,0xf6,0xf1} };
	extern "C" const GUID __declspec(selectany) IID_Projects =
	{ 0xe3ec0add,0x31b3,0x461f,{0x83,0x03,0x8a,0x5e,0x69,0x31,0x25,0x7a} };
	extern "C" const GUID __declspec(selectany) IID_Properties =
	{ 0x4cc8ccf5,0xa926,0x4646,{0xb1,0x7f,0xb4,0x94,0x0c,0xae,0xd4,0x72} };
	extern "C" const GUID __declspec(selectany) IID_Property =
	{ 0x7b988e06,0x2581,0x485e,{0x93,0x22,0x04,0x88,0x1e,0x06,0x00,0xd0} };
	extern "C" const GUID __declspec(selectany) IID_ConfigurationManager =
	{ 0x9043fda1,0x345b,0x4364,{0x90,0x0f,0xbc,0x85,0x98,0xeb,0x8e,0x4f} };
	extern "C" const GUID __declspec(selectany) IID_Configuration =
	{ 0x90813589,0xfe21,0x4aa4,{0xa2,0xe5,0x05,0x3f,0xd2,0x74,0xe9,0x80} };
	extern "C" const GUID __declspec(selectany) IID_Configurations =
	{ 0xb6b4c8d6,0x4d27,0x43b9,{0xb4,0x5c,0x52,0xbd,0x16,0xb6,0xba,0x38} };
	extern "C" const GUID __declspec(selectany) IID_OutputGroups =
	{ 0xf9fa748e,0xe302,0x44cf,{0x89,0x1b,0xe2,0x63,0x18,0x9d,0x58,0x5e} };
	extern "C" const GUID __declspec(selectany) IID_OutputGroup =
	{ 0xa3a80783,0x875f,0x435b,{0x96,0x39,0xe5,0xce,0x88,0x8d,0xf7,0x37} };
	extern "C" const GUID __declspec(selectany) IID_CodeModel =
	{ 0x0cfbc2b4,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeElements =
	{ 0x0cfbc2b5,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeElement =
	{ 0x0cfbc2b6,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_TextPoint =
	{ 0x7f59e94e,0x4939,0x40d2,{0x9f,0x7f,0xb7,0x65,0x1c,0x25,0x90,0x5d} };
	extern "C" const GUID __declspec(selectany) IID_VirtualPoint =
	{ 0x42320454,0x626c,0x4dd0,{0x9e,0xcb,0x35,0x7c,0x4f,0x19,0x66,0xd8} };
	extern "C" const GUID __declspec(selectany) IID_CodeAttribute =
	{ 0x0cfbc2be,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_TextDocument =
	{ 0xcb218890,0x1382,0x472b,{0x91,0x18,0x78,0x27,0x00,0xc8,0x81,0x15} };
	extern "C" const GUID __declspec(selectany) IID_CommandWindow =
	{ 0x509b9955,0x7303,0x48c9,{0x90,0xd4,0xe1,0x65,0xb9,0x74,0xe6,0xba} };
	extern "C" const GUID __declspec(selectany) IID__TextEditorEventsRoot =
	{ 0xb3c38885,0xb288,0x44a8,{0xb2,0x90,0x34,0xfe,0x63,0xbf,0x3c,0x76} };
	extern "C" const GUID __declspec(selectany) IID_Document =
	{ 0x63eb5c39,0xca8f,0x498e,{0x9a,0x66,0x6d,0xd4,0xa2,0x7a,0xc9,0x5b} };
	extern "C" const GUID __declspec(selectany) IID_Documents =
	{ 0x9e2cf3ea,0x140f,0x413e,{0xbd,0x4b,0x7d,0x46,0x74,0x0c,0xd2,0xf4} };
	extern "C" const GUID __declspec(selectany) IID_Events =
	{ 0x134170f8,0x93b1,0x42dd,{0x9f,0x89,0xa2,0xac,0x70,0x10,0xba,0x07} };
	extern "C" const GUID __declspec(selectany) IID_IVsExtensibility =
	{ 0x3c536122,0x57b1,0x46de,{0xab,0x34,0xac,0xc5,0x24,0x14,0x00,0x93} };
	extern "C" const GUID __declspec(selectany) IID__DocumentEventsRoot =
	{ 0xdc5437f4,0xf114,0x11d2,{0xaa,0xcf,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_TextSelection =
	{ 0x1fa0e135,0x399a,0x4d2c,{0xa4,0xfe,0xd2,0x1e,0x24,0x80,0xf9,0x21} };
	extern "C" const GUID __declspec(selectany) IID_TextRanges =
	{ 0xb6422e9c,0x9efd,0x4f87,{0xbd,0xdc,0xc7,0xfd,0x8f,0x2f,0xd3,0x03} };
	extern "C" const GUID __declspec(selectany) IID_EditPoint =
	{ 0xc1ffe800,0x028b,0x4475,{0xa9,0x07,0x14,0xf5,0x1f,0x19,0xbb,0x7d} };
	extern "C" const GUID __declspec(selectany) IID_TextRange =
	{ 0x72767524,0xe3b3,0x43d0,{0xbb,0x46,0xbb,0xe1,0xd5,0x56,0xa9,0xff} };
	extern "C" const GUID __declspec(selectany) IID_TextPane =
	{ 0x0a3bf283,0x05f8,0x4669,{0x9b,0xcb,0xa8,0x4b,0x64,0x23,0x34,0x9a} };
	extern "C" const GUID __declspec(selectany) IID_TextPanes =
	{ 0xd9013d31,0x3652,0x46b2,{0xa2,0x5a,0x29,0xa8,0x81,0xb9,0xf8,0x6b} };
	extern "C" const GUID __declspec(selectany) IID_TextWindow =
	{ 0x2fc54dc9,0x922b,0x44eb,{0x8c,0xc0,0xba,0x18,0x25,0x84,0xdc,0x4b} };
	extern "C" const GUID __declspec(selectany) IID_CodeType =
	{ 0x0cfbc2b7,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeTypeRef =
	{ 0x0cfbc2bc,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeParameter =
	{ 0x0cfbc2bd,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeFunction =
	{ 0x0cfbc2b9,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeVariable =
	{ 0x0cfbc2ba,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeNamespace =
	{ 0x0cfbc2b8,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeEnum =
	{ 0xb1f42512,0x91cd,0x4d3a,{0x8b,0x25,0xa3,0x17,0xd8,0x03,0x2b,0x24} };
	extern "C" const GUID __declspec(selectany) IID_CodeClass =
	{ 0xb1f42514,0x91cd,0x4d3a,{0x8b,0x25,0xa3,0x17,0xd8,0x03,0x2b,0x24} };
	extern "C" const GUID __declspec(selectany) IID_CodeProperty =
	{ 0x0cfbc2bb,0x0d4e,0x11d3,{0x89,0x97,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_CodeInterface =
	{ 0xb1f42510,0x91cd,0x4d3a,{0x8b,0x25,0xa3,0x17,0xd8,0x03,0x2b,0x24} };
	extern "C" const GUID __declspec(selectany) IID_CodeDelegate =
	{ 0xb1f42513,0x91cd,0x4d3a,{0x8b,0x25,0xa3,0x17,0xd8,0x03,0x2b,0x24} };
	extern "C" const GUID __declspec(selectany) IID_CodeStruct =
	{ 0xb1f42511,0x91cd,0x4d3a,{0x8b,0x25,0xa3,0x17,0xd8,0x03,0x2b,0x24} };
	extern "C" const GUID __declspec(selectany) IID_FileCodeModel =
	{ 0xed1a3f99,0x4477,0x11d3,{0x89,0xbf,0x00,0xc0,0x4f,0x68,0x8d,0xde} };
	extern "C" const GUID __declspec(selectany) IID_ContextAttributes =
	{ 0x33c5ebb8,0x244e,0x449d,{0x9c,0xee,0xfa,0xd7,0x0a,0x77,0x4e,0x59} };
	extern "C" const GUID __declspec(selectany) IID_ContextAttribute =
	{ 0x1a6e2cb3,0xb897,0x42eb,{0x96,0xbe,0xff,0x0f,0xdb,0x65,0xdb,0x2f} };
	extern "C" const GUID __declspec(selectany) IID_AddIn =
	{ 0x53a87fa1,0xce93,0x48bf,{0x95,0x8b,0xc6,0xda,0x79,0x3c,0x50,0x28} };
	extern "C" const GUID __declspec(selectany) IID_AddIns =
	{ 0x50590801,0xd13e,0x4404,{0x80,0xc2,0x5c,0xa3,0x0a,0x4d,0x0e,0xe8} };
	extern "C" const GUID __declspec(selectany) IID_OutputWindowPane =
	{ 0xffc9dfc4,0x61ca,0x4b0c,{0x83,0xc2,0x07,0x03,0xa2,0x4f,0x5c,0x16} };
	extern "C" const GUID __declspec(selectany) IID_OutputWindowPanes =
	{ 0xb02cf62a,0x9470,0x4308,{0xa5,0x21,0x96,0x75,0xfb,0xa3,0x95,0xab} };
	extern "C" const GUID __declspec(selectany) IID_OutputWindow =
	{ 0xeab0a63d,0xc3a8,0x496e,{0x9a,0xcf,0xa8,0x2c,0xef,0x6a,0x43,0xb3} };
	extern "C" const GUID __declspec(selectany) IID_TaskItem =
	{ 0x58e4d419,0x6b8c,0x4c63,{0x92,0xde,0x70,0x16,0x1c,0xd9,0x58,0x90} };
	extern "C" const GUID __declspec(selectany) IID_TaskItems =
	{ 0x4b51103d,0x513c,0x4773,{0xb5,0x6a,0x35,0x4d,0x09,0x28,0xfd,0x04} };
	extern "C" const GUID __declspec(selectany) IID_TaskList =
	{ 0x4e4f0569,0xe16a,0x4da1,{0x92,0xde,0x10,0x88,0x2a,0x4d,0xdd,0x8c} };
	extern "C" const GUID __declspec(selectany) IID_Process =
	{ 0x5c5a0070,0xf396,0x4e37,{0xa8,0x2a,0x1b,0x76,0x7e,0x27,0x2d,0xf9} };
	extern "C" const GUID __declspec(selectany) IID_Programs =
	{ 0xdc6a118a,0xbbab,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Program =
	{ 0x6a38d87c,0xbba0,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Threads =
	{ 0x6aa23fb4,0xbba1,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Thread =
	{ 0x9407f466,0xbba1,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_StackFrames =
	{ 0x4ed85664,0xbba2,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_StackFrame =
	{ 0x1342d0d8,0xbba3,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Expressions =
	{ 0x2685337a,0xbb9e,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Expression =
	{ 0x27adc812,0xbb07,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Debugger =
	{ 0x338fb9a0,0xbae5,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Processes =
	{ 0x9f379969,0x5eac,0x4a54,{0xb2,0xbc,0x69,0x46,0xcf,0xfb,0x56,0xef} };
	extern "C" const GUID __declspec(selectany) IID_Breakpoints =
	{ 0x25968106,0xbafb,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Breakpoint =
	{ 0x11c5114c,0xbb00,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Languages =
	{ 0xa4f4246c,0xc131,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID_Language =
	{ 0xb3ccfa68,0xc145,0x11d2,{0x8a,0xd1,0x00,0xc0,0x4f,0x79,0xe4,0x79} };
	extern "C" const GUID __declspec(selectany) IID__Solution =
	{ 0x26f6cc4b,0x7a48,0x4e4d,{0x8a,0xf5,0x9e,0x96,0x02,0x32,0xe0,0x5f} };
	extern "C" const GUID __declspec(selectany) IID_SolutionBuild =
	{ 0xa3c1c40c,0x9218,0x4d4c,{0x9d,0xaa,0x07,0x5f,0x64,0xf6,0x92,0x2c} };
	extern "C" const GUID __declspec(selectany) IID_SolutionConfiguration =
	{ 0x60aaad75,0xcb8d,0x4c62,{0x99,0x59,0x24,0xd6,0xa6,0xa5,0x0d,0xe7} };
	extern "C" const GUID __declspec(selectany) IID_SolutionConfigurations =
	{ 0x23e78ed7,0xc9e1,0x462d,{0x8b,0xc6,0x36,0x60,0x03,0x48,0x6e,0xd9} };
	extern "C" const GUID __declspec(selectany) IID_SolutionContexts =
	{ 0x0685b546,0xfb84,0x4917,{0xab,0x98,0x98,0xd4,0x0f,0x89,0x2d,0x61} };
	extern "C" const GUID __declspec(selectany) IID_SolutionContext =
	{ 0xfc6a1a82,0x9c8a,0x47bb,{0xa0,0x46,0x6e,0x96,0x5d,0xf5,0xa9,0x9b} };
	extern "C" const GUID __declspec(selectany) IID_BuildDependencies =
	{ 0xead260eb,0x1e5b,0x450a,{0xb6,0x28,0x4c,0xfa,0xda,0x11,0xb4,0xa1} };
	extern "C" const GUID __declspec(selectany) IID_BuildDependency =
	{ 0x9c5ceaac,0x062f,0x4434,{0xa2,0xed,0x78,0xab,0x4d,0x61,0x34,0xfe} };
	extern "C" const GUID __declspec(selectany) IID_Commands =
	{ 0xe6b96cac,0xb8c7,0x40ae,{0xb7,0x05,0x5c,0x81,0x87,0x8c,0x4a,0x9e} };
	extern "C" const GUID __declspec(selectany) IID_Command =
	{ 0x5fe10fb0,0x91a1,0x4e55,{0xba,0xaa,0xec,0xca,0xe5,0xcc,0xeb,0x94} };
	extern "C" const GUID __declspec(selectany) IID_SelectedItems =
	{ 0x6caa67cf,0x43ae,0x4184,{0xaa,0xab,0x02,0x00,0xdd,0xf6,0xb2,0x40} };
	extern "C" const GUID __declspec(selectany) IID_SelectedItem =
	{ 0x049d2cdf,0x3731,0x4cb6,{0xa2,0x33,0xbe,0x97,0xbc,0xe9,0x22,0xd3} };
	extern "C" const GUID __declspec(selectany) IID_SelectionContainer =
	{ 0x02273422,0x8dd4,0x4a9f,{0x8a,0x8b,0xd7,0x04,0x43,0xd5,0x10,0xf4} };
	extern "C" const GUID __declspec(selectany) IID_WindowConfigurations =
	{ 0xe577442a,0x98e1,0x46c5,{0xbd,0x2e,0xd2,0x58,0x07,0xec,0x81,0xce} };
	extern "C" const GUID __declspec(selectany) IID_WindowConfiguration =
	{ 0x41d02413,0x8a67,0x4c28,{0xa9,0x80,0xad,0x75,0x39,0xed,0x41,0x5d} };
	extern "C" const GUID __declspec(selectany) IID_ToolBox =
	{ 0x56fcd5af,0x7f17,0x4c5c,{0xaa,0x8d,0xae,0x2b,0xb2,0xdd,0xbf,0x38} };
	extern "C" const GUID __declspec(selectany) IID_ToolBoxTab =
	{ 0xce2def9e,0x3387,0x4bf2,{0x96,0x7b,0xa1,0xf7,0xf7,0x0d,0xf3,0x25} };
	extern "C" const GUID __declspec(selectany) IID_ToolBoxTabs =
	{ 0xcf177b52,0x4f2f,0x42a0,{0x8d,0xa3,0xce,0x78,0x67,0x9a,0x0d,0x2d} };
	extern "C" const GUID __declspec(selectany) IID_ToolBoxItems =
	{ 0x395c7dfb,0xf158,0x431c,{0x8f,0x43,0xdd,0xa8,0x3b,0x4e,0xf5,0x4e} };
	extern "C" const GUID __declspec(selectany) IID_ToolBoxItem =
	{ 0x46538d1b,0x4d81,0x4002,{0x8b,0xb4,0xdb,0xdb,0x65,0xba,0xbb,0x23} };
	extern "C" const GUID __declspec(selectany) IID_UIHierarchyItem =
	{ 0xfbd0d024,0x09cd,0x4d9f,{0x9e,0x2b,0xca,0xcd,0x62,0x84,0x26,0xa5} };
	extern "C" const GUID __declspec(selectany) IID_UIHierarchyItems =
	{ 0xdb8406b0,0xa916,0x449c,{0xa2,0x77,0xbb,0x04,0x02,0x8f,0x43,0x94} };
	extern "C" const GUID __declspec(selectany) IID_UIHierarchy =
	{ 0x72a2a2ef,0xc209,0x408c,{0xa3,0x77,0x76,0x87,0x17,0x74,0xad,0xb7} };

} // namespace EnvDTE

#pragma pack(pop)
