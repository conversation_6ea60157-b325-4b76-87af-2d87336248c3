using Cysharp.Threading.Tasks;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace DianQiXianLu {
    public class Starter : MonoBehaviour {
        [SerializeField] private Button btnEnter;
        [SerializeField] private RectTransform bg;
        [SerializeField] private Image title;

        private void Awake() {
            btnEnter.onClick.AddListener(() => {
                OnBtnEnter();
                btnEnter.image.rectTransform.DOKill();
                btnEnter.image.rectTransform.localScale = Vector3.one;
                btnEnter.image.rectTransform.DOPunchScale(Vector3.one * -0.1f, 0.15f, 0, 1f);
                AudioManager.Instance.Play2DAudio(AudioManager.Audio2D.Enter);
            });
        }

        private void Start() {
            var voice = VoiceManager.Instance;

            DOTween.Sequence()
                   .AppendInterval(0.2f)
                   .AppendCallback(() => { voice.PlayAsync("欢迎使用电气线路安装与维护,虚拟仿真实训系统", cancellationToken: destroyCancellationToken).Forget(); })
                   .Join(bg.DOScale(Vector3.one * 1.15f, 2f).From().SetEase(Ease.OutQuad))
                   .Join(title.rectTransform.DOScale(0.75f, 1f).From().SetEase(Ease.InOutQuad).SetDelay(0.2f))
                   .Join(title.DOFade(0, 1f).From().SetEase(Ease.Linear).SetDelay(0.2f))
                   .Join(btnEnter.image.rectTransform.DOScale(0, 0.5f).From().SetEase(Ease.InOutQuad).SetDelay(0.3f))
                   .OnComplete(() => { btnEnter.image.rectTransform.DOScale(1.1f, 1f).SetLoops(-1, LoopType.Yoyo).SetEase(Ease.InOutQuad); });
        }

        private void OnBtnEnter() {
            Enter();
        }

        private bool isLoaded;

        private void Enter() {
            if (isLoaded) return;

            isLoaded = true;
            // SceneManager.LoadSceneAsync("Menu", LoadSceneMode.Single);
            LoadScreen.LoadScene(new[] { "Menu" });
        }
    }
}