﻿using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

public class OperationTaskMoChuang_5 : OperationTaskBase
{
    private GrinderController grinderController;
    [SerializeField] private GrinderController.MachineState originState;
    
    private void Awake()
    {
        grinderController = FindObjectOfType<GrinderController>();
    }

    public override async UniTask StartOperationAsync(CancellationToken cancellationToken)
    {
        grinderController.machineState = originState;
        
        // 操作提示词：合上照明灯开关SA，启动机床照明灯。
        // 机床操作：机床照明灯高亮，点击开关切换，打开照明灯，需要有相应的光照效果。
        // 电路图变化：SA按下，灯EL点亮工作（参考视频）
        WorkSpaceUI.Instance.ShowMsg("合上照明灯开关SA，启动机床照明灯").Forget();
        await grinderController.ClickSAButtonTask(true, cancellationToken);
        WorkSpaceUI.Instance.ShowMsg("照明灯已打开，请查看电路图变化", 5f).Forget();
        await VideoController.Instance.PlayVideoAsync("file://Videos/M7130.mp4", 120, 125, false, cancellationToken);
        
        // 操作提示词：关闭照明灯开关SA，机床照明灯关闭。
        // 机床操作：机床照明灯高亮，点击开关切换，关闭照明灯，对应光照效果变回。
        // 电路图变化：SA分开，灯EL停止工作
        WorkSpaceUI.Instance.ShowMsg("关闭照明灯开关SA，机床照明灯关闭").Forget();
        await grinderController.ClickSAButtonTask(false, cancellationToken);
        WorkSpaceUI.Instance.ShowMsg("照明灯已关闭，请查看电路图变化", 5f).Forget();
        await VideoController.Instance.PlayVideoAsync("file://Videos/M7130.mp4", 120, 125, false, cancellationToken);
        WorkSpaceUI.Instance.ShowMsg("操作完成", 3f).Forget();
    }
}