using UnityEngine;
using UnityEngine.Video;

namespace DianQiXianLu {
    [CreateAssetMenu(fileName = "DeviceData", menuName = "DeviceData", order = 1)]
    public class DeviceData : ScriptableObject {
        public int id;
        public string deviceName;
        public string deviceDescription;
        public Texture previewImgSource;
        public ControlDeviceData[] controlDevices;
        public string workScene;
        public Texture2D mainCircuitryPicture;
        public Texture2D controlCircuitryPicture;
        public Texture2D externalCircuitryPicture;
    }
}