%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: RockFloating04_Mat 1
  m_Shader: {fileID: 4800000, guid: 8d2bb70cbf9db8d4da26e15b26e74248, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 30e19b2cfa32c904e841a50f81f29948, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 8ba1548737f50a94f85de65349abd43b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightRampTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 30e19b2cfa32c904e841a50f81f29948, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: c9a0bf51bfbd12349a4214462466c9e0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: 5f0402585ae3c904eae3ccdbbdabf71f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AdaptiveThicnkess: 0.014
    - _AdditionalLightsAmount: 1
    - _AdditionalLightsFaloff: 1
    - _AdditionalLightsIntesity: 1
    - _AdditionalLightsSmoothnessMultiplier: 1
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DiffusePosterizeOffset: -0.319
    - _DiffusePosterizePower: 1
    - _DiffusePosterizeSteps: 3
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossinessSource: 0
    - _GlossyReflections: 1
    - _IndirectLightStrength: 1
    - _LightRampOffset: 0
    - _MainLightIntesity: 1
    - _Metallic: 0
    - _Mode: 0
    - _NormalMapStrength: 1
    - _OcclusionStrength: 1
    - _OutlineTextureStrength: 0
    - _OutlineType: 0
    - _Parallax: 0.02
    - _QueueOffset: 0
    - _RampDiffuseTextureLoaded: 1
    - _ReceiveShadows: 1
    - _RimPower: 12
    - _RimSmoothness: 0
    - _RimSplitColor: 0
    - _RimThickness: 1
    - _Shininess: 0
    - _Smoothness: 0
    - _SmoothnessMultiplier: 1
    - _SmoothnessSource: 0
    - _SmoothnessTextureChannel: 0
    - _SpecSource: 0
    - _SpecularFaloff: 0
    - _SpecularHighlights: 1
    - _SpecularPosterizeSteps: 15
    - _SpecularShadowMask: 0
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _StepOffset: -0.232
    - _Strength: 0
    - _Surface: 0
    - _Thicnkess: 0.0898
    - _UVSec: 0
    - _UseAdditionalLightsDiffuse: 1
    - _UseEmission: 1
    - _UseEnvironmentRefletion: 0
    - _UseLightRamp: 2
    - _UseOutline: 1
    - _UseRimLight: 0
    - _UseShadows: 1
    - _UseSpecular: 1
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 0}
    - _RimColor: {r: 1, g: 1, b: 1, a: 0}
    - _RimShadowColor: {r: 0, g: 0.055514283, b: 0.9622641, a: 0}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &3836724559551190398
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
