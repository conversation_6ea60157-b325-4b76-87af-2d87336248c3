using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GrinderDiBan : MonoBehaviour
{
    [SerializeField] private Transform root;
    [SerializeField] Vector3 originPos;
    [SerializeField] private Vector3 outPos;
    [SerializeField] private float duration = 2f;
    
    public bool isMoving = false;

    private void Update()
    {
        if (isMoving)
        {
            // root以duration为周期在originPos和outPos之间来回移动
            root.localPosition = originPos + (outPos - originPos) * (1 + Mathf.Sin(Time.time / duration * Mathf.PI)) / 2;
        }
        else
        {
            root.localPosition = Vector3.Lerp(root.localPosition, originPos, Time.deltaTime * 2f);
        }
    }
}
