fileFormatVersion: 2
guid: 8837f192a88abd84da9f1907d0b6bdb4
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: Body
    100002: Chest
    100004: //RootNode
    100006: Head
    100008: Hips
    100010: Jaw
    100012: JawEnd
    100014: LeftEye
    100016: LeftFoot
    100018: LeftHand
    100020: LeftIndexDistal
    100022: LeftIndexDistalEnd
    100024: LeftIndexIntermediate
    100026: LeftIndexProximal
    100028: LeftLowerArm
    100030: LeftLowerLeg
    100032: LeftMiddleDistal
    100034: LeftMiddleDistalEnd
    100036: LeftMiddleIntermediate
    100038: LeftMiddleProximal
    100040: LeftPinkyDistal
    100042: LeftPinkyDistalEnd
    100044: LeftPinkyIntermediate
    100046: LeftPinkyProximal
    100048: LeftRingDistal
    100050: LeftRingDistalEnd
    100052: LeftRingIntermediate
    100054: LeftRingProximal
    100056: LeftShoulder
    100058: LeftThumbDistal
    100060: LeftThumbDistalEnd
    100062: LeftThumbIntermediate
    100064: LeftThumbProximal
    100066: LeftToes
    100068: LeftToesEnd
    100070: LeftUpperArm
    100072: LeftUpperLeg
    100074: Neck
    100076: RightEye
    100078: RightFoot
    100080: RightHand
    100082: RightIndexDistal
    100084: RightIndexDistalEnd
    100086: RightIndexIntermediate
    100088: RightIndexProximal
    100090: RightLowerArm
    100092: RightLowerLeg
    100094: RightMiddleDistal
    100096: RightMiddleDistalEnd
    100098: RightMiddleIntermediate
    100100: RightMiddleProximal
    100102: RightPinkyDistal
    100104: RightPinkyDistalEnd
    100106: RightPinkyIntermediate
    100108: RightPinkyProximal
    100110: RightRingDistal
    100112: RightRingDistalEnd
    100114: RightRingIntermediate
    100116: RightRingProximal
    100118: RightShoulder
    100120: RightThumbDistal
    100122: RightThumbDistalEnd
    100124: RightThumbIntermediate
    100126: RightThumbProximal
    100128: RightToes
    100130: RightToesEnd
    100132: RightUpperArm
    100134: RightUpperLeg
    100136: Skeleton
    100138: Spine
    100140: UpperChest
    100142: Root
    100144: LProp_root
    100146: LProp_target
    100148: RProp_root
    100150: RProp_target
    400000: Body
    400002: Chest
    400004: //RootNode
    400006: Head
    400008: Hips
    400010: Jaw
    400012: JawEnd
    400014: LeftEye
    400016: LeftFoot
    400018: LeftHand
    400020: LeftIndexDistal
    400022: LeftIndexDistalEnd
    400024: LeftIndexIntermediate
    400026: LeftIndexProximal
    400028: LeftLowerArm
    400030: LeftLowerLeg
    400032: LeftMiddleDistal
    400034: LeftMiddleDistalEnd
    400036: LeftMiddleIntermediate
    400038: LeftMiddleProximal
    400040: LeftPinkyDistal
    400042: LeftPinkyDistalEnd
    400044: LeftPinkyIntermediate
    400046: LeftPinkyProximal
    400048: LeftRingDistal
    400050: LeftRingDistalEnd
    400052: LeftRingIntermediate
    400054: LeftRingProximal
    400056: LeftShoulder
    400058: LeftThumbDistal
    400060: LeftThumbDistalEnd
    400062: LeftThumbIntermediate
    400064: LeftThumbProximal
    400066: LeftToes
    400068: LeftToesEnd
    400070: LeftUpperArm
    400072: LeftUpperLeg
    400074: Neck
    400076: RightEye
    400078: RightFoot
    400080: RightHand
    400082: RightIndexDistal
    400084: RightIndexDistalEnd
    400086: RightIndexIntermediate
    400088: RightIndexProximal
    400090: RightLowerArm
    400092: RightLowerLeg
    400094: RightMiddleDistal
    400096: RightMiddleDistalEnd
    400098: RightMiddleIntermediate
    400100: RightMiddleProximal
    400102: RightPinkyDistal
    400104: RightPinkyDistalEnd
    400106: RightPinkyIntermediate
    400108: RightPinkyProximal
    400110: RightRingDistal
    400112: RightRingDistalEnd
    400114: RightRingIntermediate
    400116: RightRingProximal
    400118: RightShoulder
    400120: RightThumbDistal
    400122: RightThumbDistalEnd
    400124: RightThumbIntermediate
    400126: RightThumbProximal
    400128: RightToes
    400130: RightToesEnd
    400132: RightUpperArm
    400134: RightUpperLeg
    400136: Skeleton
    400138: Spine
    400140: UpperChest
    400142: Root
    400144: LProp_root
    400146: LProp_target
    400148: RProp_root
    400150: RProp_target
    2300000: Body
    3300000: Body
    4300000: Body
    9500000: //RootNode
    13700000: Body
    2186277476908879412: ImportLogs
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: DefaultMale_Mat
    second: {fileID: 2100000, guid: d7f8b496adee38e4a9edc877d5939f5b, type: 2}
  materials:
    importMaterials: 1
    materialName: 1
    materialSearch: 2
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpperLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpperLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLowerLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLowerLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpperArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpperArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLowerArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLowerArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftThumbProximal
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftThumbIntermediate
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftThumbDistal
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftIndexProximal
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftIndexIntermediate
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftIndexDistal
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftMiddleProximal
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftMiddleIntermediate
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftMiddleDistal
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftRingProximal
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftRingIntermediate
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftRingDistal
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftPinkyProximal
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftPinkyIntermediate
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftPinkyDistal
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightThumbProximal
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightThumbIntermediate
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightThumbDistal
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightIndexProximal
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightIndexIntermediate
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightIndexDistal
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightMiddleProximal
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightMiddleIntermediate
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightMiddleDistal
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightRingProximal
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightRingIntermediate
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightRingDistal
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightPinkyProximal
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightPinkyIntermediate
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightPinkyDistal
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperChest
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: DefaultMale(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Body
      parentName: DefaultMale(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton
      parentName: DefaultMale(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Root
      parentName: Skeleton
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Root
      position: {x: 1.1827783e-16, y: 0.99710166, z: 0.011954045}
      rotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpperLeg
      parentName: Hips
      position: {x: 0.05013466, y: -0.0024317312, z: 0.089999996}
      rotation: {x: 0, y: 0, z: 0.99998796, w: 0.004908496}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLowerLeg
      parentName: LeftUpperLeg
      position: {x: -0.43576738, y: 8.0373204e-16, z: -2.3092638e-16}
      rotation: {x: 0, y: -0, z: -0.041384947, w: 0.9991433}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFoot
      parentName: LeftLowerLeg
      position: {x: -0.4248355, y: 3.2196466e-16, z: 1.9539925e-16}
      rotation: {x: 0, y: 0, z: 0.04628874, w: 0.9989281}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToes
      parentName: LeftFoot
      position: {x: -0.0758718, y: -0.17069253, z: 5.1514347e-16}
      rotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToesEnd
      parentName: LeftToes
      position: {x: -0.054048985, y: -9.722308e-18, z: 1.0302869e-15}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpperLeg
      parentName: Hips
      position: {x: 0.05013466, y: -0.0024317356, z: -0.089999996}
      rotation: {x: 0, y: -0, z: -0.004908496, w: 0.99998796}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLowerLeg
      parentName: RightUpperLeg
      position: {x: 0.43576697, y: -4.867446e-10, z: 1.9539925e-16}
      rotation: {x: 0, y: -0, z: -0.041384947, w: 0.9991433}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFoot
      parentName: RightLowerLeg
      position: {x: 0.42483592, y: 0.00000004328955, z: 1.9539925e-16}
      rotation: {x: 0, y: 0, z: 0.04628874, w: 0.9989281}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToes
      parentName: RightFoot
      position: {x: 0.0758718, y: 0.1706926, z: 1.0658141e-16}
      rotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToesEnd
      parentName: RightToes
      position: {x: 0.054049, y: 4.2188474e-17, z: 3.5527136e-17}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0.0763707, y: 0, z: 1.3457705e-17}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Chest
      parentName: Spine
      position: {x: -0.102159195, y: 0, z: 3.418214e-17}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: UpperChest
      parentName: Chest
      position: {x: -0.099863835, y: 3.837587e-10, z: 4.2860933e-17}
      rotation: {x: 2.326362e-19, y: 6.428443e-21, z: 0.02762249, w: 0.9996185}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: UpperChest
      position: {x: -0.22944902, y: 0, z: 3.8648323e-18}
      rotation: {x: 0, y: -0, z: -0.16252996, w: 0.98670363}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -0.09720324, y: -3.5527136e-17, z: -7.336409e-18}
      rotation: {x: 0, y: 0, z: 0.13521273, w: 0.9908166}
      scale: {x: 1, y: 1, z: 1}
    - name: Jaw
      parentName: Head
      position: {x: 0.020377725, y: 0.023429718, z: 0.000011341149}
      rotation: {x: -0.00011591564, y: 0.00007256035, z: -0.8476265, w: 0.53059345}
      scale: {x: 1, y: 1, z: 1}
    - name: JawEnd
      parentName: Jaw
      position: {x: -0.094900064, y: 1.8092883e-17, z: 4.9371638e-17}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEye
      parentName: Head
      position: {x: -0.04963885, y: 0.11306744, z: -0.029689595}
      rotation: {x: 0, y: -0, z: -0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEye
      parentName: Head
      position: {x: -0.04963602, y: 0.11306743, z: 0.03}
      rotation: {x: 0, y: -0, z: -0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftShoulder
      parentName: UpperChest
      position: {x: -0.14809349, y: 0.018417658, z: 0.041293867}
      rotation: {x: -0.48599797, y: 0.51362044, z: 0.48599797, w: 0.51362044}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpperArm
      parentName: LeftShoulder
      position: {x: -0.14904714, y: 0.021559998, z: -0.026150007}
      rotation: {x: 1.0340241e-25, y: -0.0096692005, z: 0.00000011276885, w: 0.99995327}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLowerArm
      parentName: LeftUpperArm
      position: {x: -0.25980794, y: 1.2984311e-16, z: 1.3635393e-16}
      rotation: {x: 0.000000001217143, y: 0.010789105, z: -0.00000011280568, w: 0.9999418}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHand
      parentName: LeftLowerArm
      position: {x: -0.25833747, y: -4.1848454e-17, z: 7.817271e-16}
      rotation: {x: 0, y: -0.0011199615, z: -0, w: 0.9999994}
      scale: {x: 1, y: 1, z: 1}
    - name: LProp_root
      parentName: LeftHand
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.000000001091122, y: 3.417706e-16, z: 2.756615e-11, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LProp_target
      parentName: LProp_root
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftThumbProximal
      parentName: LeftHand
      position: {x: -0.032763172, y: -0.019399941, z: 0.032587297}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftThumbIntermediate
      parentName: LeftThumbProximal
      position: {x: -0.015590345, y: -0.02269687, z: -0.0022699998}
      rotation: {x: 0.00000017615224, y: 0.00000044237117, z: 0.46174863, w: 0.8870109}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftThumbDistal
      parentName: LeftThumbIntermediate
      position: {x: -0.03139568, y: 0, z: 2.8111882e-17}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftThumbDistalEnd
      parentName: LeftThumbDistal
      position: {x: -0.032299604, y: -1.2479823e-16, z: -3.2554504e-16}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyProximal
      parentName: LeftHand
      position: {x: -0.09005295, y: -0.01086029, z: -0.033707228}
      rotation: {x: 1, y: 0.00000048059263, z: 0.000000045818194, w: 0.000000002879936}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyIntermediate
      parentName: LeftPinkyProximal
      position: {x: -0.020319965, y: -0.000000009765624, z: -9.310237e-10}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyDistal
      parentName: LeftPinkyIntermediate
      position: {x: -0.019660275, y: -0.000000018897165, z: -0.0000000018015959}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyDistalEnd
      parentName: LeftPinkyDistal
      position: {x: -0.021771478, y: -0.000000020926425, z: -0.0000000019950588}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingProximal
      parentName: LeftHand
      position: {x: -0.10098182, y: -0.008680271, z: -0.01518866}
      rotation: {x: 1, y: -0, z: 0, w: 0.000000002879931}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingIntermediate
      parentName: LeftRingProximal
      position: {x: -0.028015258, y: -0.00000006713867, z: -6.120983e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingDistal
      parentName: LeftRingIntermediate
      position: {x: -0.029936645, y: -0.00000014348656, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingDistalEnd
      parentName: LeftRingDistal
      position: {x: -0.026132211, y: -0.00000012525189, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleProximal
      parentName: LeftHand
      position: {x: -0.10176471, y: -0.009200126, z: 0.0076735215}
      rotation: {x: 1, y: 0.000001953623, z: 5.6263126e-15, w: 0.0000000028799376}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleIntermediate
      parentName: LeftMiddleProximal
      position: {x: -0.03811528, y: -0.00000007446289, z: 2.8247632e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleDistal
      parentName: LeftMiddleIntermediate
      position: {x: -0.027341224, y: -0.0000001068289, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleDistalEnd
      parentName: LeftMiddleDistal
      position: {x: -0.029609164, y: -0.00000011569029, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexProximal
      parentName: LeftHand
      position: {x: -0.10144374, y: -0.010422724, z: 0.032279857}
      rotation: {x: 1, y: -0.00000080360593, z: -0.00000003485639, w: 0.0000000028799356}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexIntermediate
      parentName: LeftIndexProximal
      position: {x: -0.03038064, y: 0.000000024414062, z: 0.0000000010589601}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexDistal
      parentName: LeftIndexIntermediate
      position: {x: -0.023295304, y: 0.00000006185455, z: 0.0000000026829414}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexDistalEnd
      parentName: LeftIndexDistal
      position: {x: -0.027728153, y: 0.000000044565017, z: 0.000000001933008}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightShoulder
      parentName: UpperChest
      position: {x: -0.14809349, y: 0.018417655, z: -0.041293897}
      rotation: {x: 0.51362044, y: 0.48599797, z: -0.51362044, w: 0.48599797}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpperArm
      parentName: RightShoulder
      position: {x: 0.14904709, y: -0.021559998, z: 0.026150005}
      rotation: {x: 0.0000000010905377, y: -0.0096692005, z: 0.0000001127794, w: 0.99995327}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLowerArm
      parentName: RightUpperArm
      position: {x: 0.2598076, y: -0.000000058593667, z: 0.0000000065556907}
      rotation: {x: 0.000000001217143, y: 0.010789105, z: -0.00000011280568, w: 0.9999418}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHand
      parentName: RightLowerArm
      position: {x: 0.25833765, y: 8.2224006e-13, z: -0.0000000040112766}
      rotation: {x: 0, y: -0.0011199615, z: -0, w: 0.9999994}
      scale: {x: 1, y: 1, z: 1}
    - name: RProp_root
      parentName: RightHand
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 1, y: 6.47687e-12, z: -4.49967e-15, w: 0.000000002181506}
      scale: {x: 1, y: 1, z: 1}
    - name: RProp_target
      parentName: RProp_root
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightThumbProximal
      parentName: RightHand
      position: {x: 0.032763, y: 0.019399999, z: -0.03258728}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: RightThumbIntermediate
      parentName: RightThumbProximal
      position: {x: 0.015591, y: 0.0226969, z: 0.0022699998}
      rotation: {x: 0.00000017597144, y: 0.00000044227704, z: 0.46174863, w: 0.8870109}
      scale: {x: 1, y: 1, z: 1}
    - name: RightThumbDistal
      parentName: RightThumbIntermediate
      position: {x: 0.031395752, y: -0.00000016593874, z: 0.00000001956757}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightThumbDistalEnd
      parentName: RightThumbDistal
      position: {x: 0.032299694, y: 0.00000041872633, z: 0.000000020130532}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightPinkyProximal
      parentName: RightHand
      position: {x: 0.09005299, y: 0.01086, z: 0.03370722}
      rotation: {x: 1, y: 0.0000009611853, z: 0.00000009163637, w: 0.00000048049026}
      scale: {x: 1, y: 1, z: 1}
    - name: RightPinkyIntermediate
      parentName: RightPinkyProximal
      position: {x: 0.02032, y: 0.0000000390623, z: 0.0000000037241212}
      rotation: {x: 0.00000067755235, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightPinkyDistal
      parentName: RightPinkyIntermediate
      position: {x: 0.01966, y: 0.00000003779355, z: 0.0000000036031094}
      rotation: {x: 0.00000052042185, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightPinkyDistalEnd
      parentName: RightPinkyDistal
      position: {x: 0.021772001, y: 0.000000041853575, z: 0.000000003990134}
      rotation: {x: 0.00000086590364, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingProximal
      parentName: RightHand
      position: {x: 0.100981995, y: 0.00868, z: 0.0151886195}
      rotation: {x: 1, y: -0, z: 0, w: 6.123234e-17}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingIntermediate
      parentName: RightRingProximal
      position: {x: 0.028015, y: -3.6322944e-13, z: 2.509104e-16}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingDistal
      parentName: RightRingIntermediate
      position: {x: 0.029937, y: -3.8795632e-13, z: 2.7311487e-16}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingDistalEnd
      parentName: RightRingDistal
      position: {x: 0.026131999, y: -3.3821833e-13, z: 2.3536727e-16}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleProximal
      parentName: RightHand
      position: {x: 0.101765, y: 0.0092, z: -0.0076735197}
      rotation: {x: 1, y: 0.000003907246, z: 7.633312e-12, w: 0.0000019536299}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleIntermediate
      parentName: RightMiddleProximal
      position: {x: 0.038115, y: 0.00000029784886, z: 1.164116e-12}
      rotation: {x: 0.0000027608096, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleDistal
      parentName: RightMiddleIntermediate
      position: {x: 0.027341, y: 0.00000021365567, z: -3.446715e-13}
      rotation: {x: 0.0000021149021, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleDistalEnd
      parentName: RightMiddleDistal
      position: {x: 0.029609999, y: 0.00000023138672, z: -1.3519946e-12}
      rotation: {x: 0.000003517392, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexProximal
      parentName: RightHand
      position: {x: 0.10144399, y: 0.010419999, z: -0.03227988}
      rotation: {x: 1, y: -0.0000016072119, z: -0.00000006971279, w: 0.0000008035582}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexIntermediate
      parentName: RightIndexProximal
      position: {x: 0.030381, y: -0.00000009765779, z: -0.0000000042359667}
      rotation: {x: 0.0000011344717, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexDistal
      parentName: RightIndexIntermediate
      position: {x: 0.023294998, y: -0.000000074880305, z: -0.000000003247809}
      rotation: {x: 0.00000087025205, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexDistalEnd
      parentName: RightIndexDistal
      position: {x: 0.027727999, y: -0.000000089129905, z: -0.0000000038657064}
      rotation: {x: 0.0000014476384, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
