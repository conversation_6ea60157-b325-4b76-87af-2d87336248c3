<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/CMakeLists.txt</key>
		<data>
		cOtLXxofm3lG1qCCrIvrwNXC1XA=
		</data>
		<key>Resources/Cholesky</key>
		<data>
		rbUhwzsxHGtY4Odila0J8dDsV7k=
		</data>
		<key>Resources/CholmodSupport</key>
		<data>
		JcYHIXmbeMaeHAwwYAmP0vUX5r0=
		</data>
		<key>Resources/Core</key>
		<data>
		1e57zXoa4MfI2FfPzMoxe9qkZFM=
		</data>
		<key>Resources/Dense</key>
		<data>
		l6aicKxqX1+TG9mrHj2EXaWnn/I=
		</data>
		<key>Resources/Eigen</key>
		<data>
		T4sCLK+7QbphwVpDFbY6bkR8w24=
		</data>
		<key>Resources/Eigenvalues</key>
		<data>
		QxGMXYy3HLNE6th4DJ2CZNO5+Gg=
		</data>
		<key>Resources/Geometry</key>
		<data>
		6bH+dsNAF2978gZVJzjyBQemqdw=
		</data>
		<key>Resources/Householder</key>
		<data>
		9lmBF6j1KedsAiS56htztnuZTWw=
		</data>
		<key>Resources/IterativeLinearSolvers</key>
		<data>
		605Q7NK74EEQXA5Z4zmB+EBlFk8=
		</data>
		<key>Resources/Jacobi</key>
		<data>
		Me3Qwr2J524HN3kmNL2jw6Oyrco=
		</data>
		<key>Resources/LU</key>
		<data>
		B2LyoXHxqVkjJiOGrnjWbagyjjs=
		</data>
		<key>Resources/MetisSupport</key>
		<data>
		N0SrA+L+jFsTerjHBpj1FQwUko0=
		</data>
		<key>Resources/OrderingMethods</key>
		<data>
		13PUHtslb3Pl8BZZPML035HSo+s=
		</data>
		<key>Resources/PaStiXSupport</key>
		<data>
		f3z7K4pYDhKZF4jZ12qik6ViZNE=
		</data>
		<key>Resources/PardisoSupport</key>
		<data>
		u0iXf0ZsM2IbFXXzJgrB/ytE1Cg=
		</data>
		<key>Resources/QR</key>
		<data>
		QXKl7/7DIw2i7Apx3qN5GA3GuhY=
		</data>
		<key>Resources/QtAlignedMalloc</key>
		<data>
		rpLY5WInNGmI/WRfJUQPjO3wpxA=
		</data>
		<key>Resources/SPQRSupport</key>
		<data>
		PhbG0vB6FuoQqcTZcXTmaSvBA5s=
		</data>
		<key>Resources/SVD</key>
		<data>
		v/sdbqyMvo2FJojRWaYGrVNQfCY=
		</data>
		<key>Resources/Sparse</key>
		<data>
		HRDaPlp1w8nkb6ljJjDbZwK8CDg=
		</data>
		<key>Resources/SparseCholesky</key>
		<data>
		ekL4dP8xYzHzS4CgaMScIxwyqLc=
		</data>
		<key>Resources/SparseCore</key>
		<data>
		kxsvRhvb2pV54xnktT8W/qGgMSA=
		</data>
		<key>Resources/SparseLU</key>
		<data>
		lK/U4MqbwhmtHbKWdoE5YfxrPLI=
		</data>
		<key>Resources/SparseQR</key>
		<data>
		wcjlNfy9k2yMG1ItF6ig+6EJme0=
		</data>
		<key>Resources/StdDeque</key>
		<data>
		IoY/3GB4PujtiSHyQua86fal3B0=
		</data>
		<key>Resources/StdList</key>
		<data>
		MNRej3tS60Pw58N2+MRA59U9mFs=
		</data>
		<key>Resources/StdVector</key>
		<data>
		+ZDwOyhJ5VynkBV0pM3nEgn13Ho=
		</data>
		<key>Resources/SuperLUSupport</key>
		<data>
		RwjfZ7P3MquMRVRW2ETZptE1GUU=
		</data>
		<key>Resources/UmfPackSupport</key>
		<data>
		M8QOb2WmVgN/4pnOv9+urwokZUw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/CMakeLists.txt</key>
		<dict>
			<key>hash</key>
			<data>
			cOtLXxofm3lG1qCCrIvrwNXC1XA=
			</data>
			<key>hash2</key>
			<data>
			6Z3rtsJEI2ZM9hDVP6sJLuqx7CMHa5Zkdt3+c68SAgI=
			</data>
		</dict>
		<key>Resources/Cholesky</key>
		<dict>
			<key>hash</key>
			<data>
			rbUhwzsxHGtY4Odila0J8dDsV7k=
			</data>
			<key>hash2</key>
			<data>
			ZiWSB5ItbHNVDzQN7dEjKix/S9k64PnC8ekY5ivWPiY=
			</data>
		</dict>
		<key>Resources/CholmodSupport</key>
		<dict>
			<key>hash</key>
			<data>
			JcYHIXmbeMaeHAwwYAmP0vUX5r0=
			</data>
			<key>hash2</key>
			<data>
			JvDRvQZ3eFOU8fTrLN33Y0I/cDwJQbPlg3PpVbATAyc=
			</data>
		</dict>
		<key>Resources/Core</key>
		<dict>
			<key>hash</key>
			<data>
			1e57zXoa4MfI2FfPzMoxe9qkZFM=
			</data>
			<key>hash2</key>
			<data>
			XZUAlmhc6e7fCZqjpfARdPxo7EHD8MMBHig0y3kz+m0=
			</data>
		</dict>
		<key>Resources/Dense</key>
		<dict>
			<key>hash</key>
			<data>
			l6aicKxqX1+TG9mrHj2EXaWnn/I=
			</data>
			<key>hash2</key>
			<data>
			UyXJ1NaT5QTuASxSda1LELE1pwHL2HwURm0uoh6iLuU=
			</data>
		</dict>
		<key>Resources/Eigen</key>
		<dict>
			<key>hash</key>
			<data>
			T4sCLK+7QbphwVpDFbY6bkR8w24=
			</data>
			<key>hash2</key>
			<data>
			sEc7XXwM2TEdSpBUgWLozxYJBEdocff7dJ6WJOmz39E=
			</data>
		</dict>
		<key>Resources/Eigenvalues</key>
		<dict>
			<key>hash</key>
			<data>
			QxGMXYy3HLNE6th4DJ2CZNO5+Gg=
			</data>
			<key>hash2</key>
			<data>
			FK3msK6JiMFj0GpeJfpW6N3dEF0AdcjQc4vzPWfTN4w=
			</data>
		</dict>
		<key>Resources/Geometry</key>
		<dict>
			<key>hash</key>
			<data>
			6bH+dsNAF2978gZVJzjyBQemqdw=
			</data>
			<key>hash2</key>
			<data>
			MelWZv7cjgpCH1wOIT0JFuroXKS0J1Eu4SSg1xdnGkk=
			</data>
		</dict>
		<key>Resources/Householder</key>
		<dict>
			<key>hash</key>
			<data>
			9lmBF6j1KedsAiS56htztnuZTWw=
			</data>
			<key>hash2</key>
			<data>
			+uLEXDVHKaNzk09RBGAkWNuziztuYfWO9bFWt7q6t0k=
			</data>
		</dict>
		<key>Resources/IterativeLinearSolvers</key>
		<dict>
			<key>hash</key>
			<data>
			605Q7NK74EEQXA5Z4zmB+EBlFk8=
			</data>
			<key>hash2</key>
			<data>
			hP+jisHvO+AFv/UAt4tSnB4WT8UGN4Nv+GSP6hJ7dPc=
			</data>
		</dict>
		<key>Resources/Jacobi</key>
		<dict>
			<key>hash</key>
			<data>
			Me3Qwr2J524HN3kmNL2jw6Oyrco=
			</data>
			<key>hash2</key>
			<data>
			xIpgdOLmWzZI3YZxV0p0zuJT2OK5SGb1QQ8SSlVYkjI=
			</data>
		</dict>
		<key>Resources/LU</key>
		<dict>
			<key>hash</key>
			<data>
			B2LyoXHxqVkjJiOGrnjWbagyjjs=
			</data>
			<key>hash2</key>
			<data>
			i4A6iQsITpNuynj68eNhunZpcYHS66XWgQ9gInPvlZU=
			</data>
		</dict>
		<key>Resources/MetisSupport</key>
		<dict>
			<key>hash</key>
			<data>
			N0SrA+L+jFsTerjHBpj1FQwUko0=
			</data>
			<key>hash2</key>
			<data>
			5G1R0wZZYkCKQDSA8snK1OfWH7UJweSmlSdHUSdJaaI=
			</data>
		</dict>
		<key>Resources/OrderingMethods</key>
		<dict>
			<key>hash</key>
			<data>
			13PUHtslb3Pl8BZZPML035HSo+s=
			</data>
			<key>hash2</key>
			<data>
			mimHU/G8AMZZHPPY+vIFOsmwyNNi5xuiE2UEyoy9jZ4=
			</data>
		</dict>
		<key>Resources/PaStiXSupport</key>
		<dict>
			<key>hash</key>
			<data>
			f3z7K4pYDhKZF4jZ12qik6ViZNE=
			</data>
			<key>hash2</key>
			<data>
			EUhTmIWLVP/BlcxR0xVD/GX+p5qM2QHvldpAXOeQ43A=
			</data>
		</dict>
		<key>Resources/PardisoSupport</key>
		<dict>
			<key>hash</key>
			<data>
			u0iXf0ZsM2IbFXXzJgrB/ytE1Cg=
			</data>
			<key>hash2</key>
			<data>
			UOxQA985n6v1GGIOIg9xxxf/OgOnIYuGUKl03DrQaxU=
			</data>
		</dict>
		<key>Resources/QR</key>
		<dict>
			<key>hash</key>
			<data>
			QXKl7/7DIw2i7Apx3qN5GA3GuhY=
			</data>
			<key>hash2</key>
			<data>
			STU0Hi8NgBNq1S6zT5VVYZC2Y93WOOZKqf/P6YrGXAM=
			</data>
		</dict>
		<key>Resources/QtAlignedMalloc</key>
		<dict>
			<key>hash</key>
			<data>
			rpLY5WInNGmI/WRfJUQPjO3wpxA=
			</data>
			<key>hash2</key>
			<data>
			m6XbWPwuNmqDPQpH5ljceOyJBE6gvFddQA131+KVZoM=
			</data>
		</dict>
		<key>Resources/SPQRSupport</key>
		<dict>
			<key>hash</key>
			<data>
			PhbG0vB6FuoQqcTZcXTmaSvBA5s=
			</data>
			<key>hash2</key>
			<data>
			T6LXO5mMB5RoLWPf+//3lb6kGn0YZlhAscNX5m/xyJ0=
			</data>
		</dict>
		<key>Resources/SVD</key>
		<dict>
			<key>hash</key>
			<data>
			v/sdbqyMvo2FJojRWaYGrVNQfCY=
			</data>
			<key>hash2</key>
			<data>
			gK7oqjJo1vSqYB1grNVgYk6Y7UfQtEDMGfNUzoaM3vY=
			</data>
		</dict>
		<key>Resources/Sparse</key>
		<dict>
			<key>hash</key>
			<data>
			HRDaPlp1w8nkb6ljJjDbZwK8CDg=
			</data>
			<key>hash2</key>
			<data>
			+5LkscC4XT6SktBksbFwC/HUiNSQXGSqI7nkD+2nVHg=
			</data>
		</dict>
		<key>Resources/SparseCholesky</key>
		<dict>
			<key>hash</key>
			<data>
			ekL4dP8xYzHzS4CgaMScIxwyqLc=
			</data>
			<key>hash2</key>
			<data>
			f+/W9qpxQFLRpn759ZjUe9+i4h8jfIIMTA430BO0kik=
			</data>
		</dict>
		<key>Resources/SparseCore</key>
		<dict>
			<key>hash</key>
			<data>
			kxsvRhvb2pV54xnktT8W/qGgMSA=
			</data>
			<key>hash2</key>
			<data>
			IyPB4xed/Gq/NIFht4wb9ezQyqj0UscMrKmhzf3Wpq4=
			</data>
		</dict>
		<key>Resources/SparseLU</key>
		<dict>
			<key>hash</key>
			<data>
			lK/U4MqbwhmtHbKWdoE5YfxrPLI=
			</data>
			<key>hash2</key>
			<data>
			z5KW+7PH1oMpAT/cdGTh1pbRrq6cd42eBtNw0Qj62aI=
			</data>
		</dict>
		<key>Resources/SparseQR</key>
		<dict>
			<key>hash</key>
			<data>
			wcjlNfy9k2yMG1ItF6ig+6EJme0=
			</data>
			<key>hash2</key>
			<data>
			l1a62EFnSRtE2ZkGE+ouiJhX7rVP/KUNWQQQhG9cZ9I=
			</data>
		</dict>
		<key>Resources/StdDeque</key>
		<dict>
			<key>hash</key>
			<data>
			IoY/3GB4PujtiSHyQua86fal3B0=
			</data>
			<key>hash2</key>
			<data>
			wYhRZ6RHglq3w1VzQiw++iPexi9bYwrDIv/mWODuUkI=
			</data>
		</dict>
		<key>Resources/StdList</key>
		<dict>
			<key>hash</key>
			<data>
			MNRej3tS60Pw58N2+MRA59U9mFs=
			</data>
			<key>hash2</key>
			<data>
			A1QOKXrpbtA+go2aQzu39aXpOqg8b1CmwnHHj5KD1mc=
			</data>
		</dict>
		<key>Resources/StdVector</key>
		<dict>
			<key>hash</key>
			<data>
			+ZDwOyhJ5VynkBV0pM3nEgn13Ho=
			</data>
			<key>hash2</key>
			<data>
			fosQYHV6EUApj/OQXfcO21mtEIP1BWgd8w3J3Jlj2Ks=
			</data>
		</dict>
		<key>Resources/SuperLUSupport</key>
		<dict>
			<key>hash</key>
			<data>
			RwjfZ7P3MquMRVRW2ETZptE1GUU=
			</data>
			<key>hash2</key>
			<data>
			iqYB8/oea/wDt5lma/OevELUSRel+yqqSJvq/WOQH3I=
			</data>
		</dict>
		<key>Resources/UmfPackSupport</key>
		<dict>
			<key>hash</key>
			<data>
			M8QOb2WmVgN/4pnOv9+urwokZUw=
			</data>
			<key>hash2</key>
			<data>
			AD1B+gjN5oscRphJn6m/nIA867J650hhWFFRn0LcVqw=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
