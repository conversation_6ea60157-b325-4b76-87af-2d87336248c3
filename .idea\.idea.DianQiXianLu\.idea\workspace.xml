<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="3" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2dd9b7f3-ba73-46bb-a819-4e5c436a4f17" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Packages/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/packages-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/packages-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/Packages/com.unity.probuilder/Settings.json" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/Packages/com.unity.probuilder/Settings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/ProjectVersion.txt" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/ProjectVersion.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5ddb5fa70dfe40d2818192400111b9ec2aa00/0d/eb02f2cd/Ease.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5ddb5fa70dfe40d2818192400111b9ec2aa00/1b/83576e18/ShortcutExtensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a655008de92846458baa16d876fb23c4171c00/13/78cda4c3/Mesh.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a655008de92846458baa16d876fb23c4171c00/6f/8c07ddb3/SceneManager.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a655008de92846458baa16d876fb23c4171c00/9f/5056750f/MonoBehaviour.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/AudioManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/Column.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/ColumnLooseTightHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/Drilling.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/DrillingDirHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/DrillingStretchHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/FasteningScrew.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/Handwheel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/ImpulseHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/WorkbenchAdvanceHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Interactable/WorkbenchHandle.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/LoadScreen.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SimpleCameraController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/UIBaseView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui@1.0.0/Runtime/UI/Core/GraphicRaycaster.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2R85gXgSWl2jltmUCPVi2tSahHT" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Discover.Launch.Via.Unity&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;rider.code.cleanup.on.save&quot;: &quot;true&quot;,
    &quot;rider.code.cleanup.on.save.profile&quot;: &quot;Built-in: Reformat &amp; Apply Syntax Style&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;actions.on.save&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;rider.external.source.directories&quot;: [
      &quot;C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\Rider2024.1\\resharper-host\\DecompilerCache&quot;,
      &quot;C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\Rider2024.1\\resharper-host\\SourcesCache&quot;,
      &quot;C:\\Users\\<USER>\\AppData\\Local\\Symbols\\src&quot;
    ]
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor">
    <configuration name="Standalone Player" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="$PROJECT_DIR$/Build\DianQiXianLu.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\DianQiXianLu\Build" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="E:\Unity\2022.3.55f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath F:\LongProjects\DianQiXianLu -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\DianQiXianLu" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="E:\Unity\2022.3.55f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath F:\LongProjects\DianQiXianLu -testResults Logs/results.xml -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\DianQiXianLu" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="UnitTests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\2022.3.8f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath F:\LongProjects\DianQiXianLu -testResults Logs/results.xml -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\DianQiXianLu" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration useDefault="false" />
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="F:\LongProjects\DianQiXianLu" />
          <option name="myCopyRoot" value="F:\LongProjects\DianQiXianLu" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="F:\LongProjects\DianQiXianLu" />
          <option name="myCopyRoot" value="F:\LongProjects\DianQiXianLu" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2dd9b7f3-ba73-46bb-a819-4e5c436a4f17" name="Changes" comment="" />
      <created>1686618982852</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1686618982852</updated>
      <workItem from="1686618988871" duration="1674000" />
      <workItem from="1686621264681" duration="173000" />
      <workItem from="1686621472484" duration="17150000" />
      <workItem from="1686704606730" duration="26284000" />
      <workItem from="1686790547304" duration="18491000" />
      <workItem from="1686880617143" duration="26586000" />
      <workItem from="1687136636669" duration="121000" />
      <workItem from="1687136784810" duration="5655000" />
      <workItem from="1687142515692" duration="7584000" />
      <workItem from="1687222781404" duration="14550000" />
      <workItem from="1687309696929" duration="22812000" />
      <workItem from="1687662548984" duration="1691000" />
      <workItem from="1687672806728" duration="635000" />
      <workItem from="1688433338369" duration="1301000" />
      <workItem from="1688536596297" duration="3908000" />
      <workItem from="1688605926546" duration="12027000" />
      <workItem from="1688632770822" duration="183000" />
      <workItem from="1696994959501" duration="10661000" />
      <workItem from="1697013489515" duration="1312000" />
      <workItem from="1697073823715" duration="12748000" />
      <workItem from="1697102871818" duration="1888000" />
      <workItem from="1697158808375" duration="5534000" />
      <workItem from="1745395023246" duration="240000" />
      <workItem from="1745395283959" duration="1737000" />
      <workItem from="1745399688245" duration="1078000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="EditMode" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>