%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: WorldSpaceGrid
  m_Shader: {fileID: 4800000, guid: 2c7f02966b074c2459610024384f0fb8, type: 3}
  m_ShaderKeywords: _ENABLEWORLDSPACE_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 229985cfd25c949498041f75324bdc75, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _EnableWorldSpace: 1
    - _Glossiness: 0.5
    - _Metallic: 0
    - _Scale: 1
    m_Colors:
    - _Color: {r: 0.61, g: 0.61, b: 0.61, a: 1}
  m_BuildTextureStacks: []
