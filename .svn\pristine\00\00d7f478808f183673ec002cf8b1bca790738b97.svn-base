using UnityEngine;

namespace DianQiXianLu.Interactable {
    public class DrillingStretchHandle : InteractableController {
        [SerializeField] private Transform pivot;
        [SerializeField] private Vector3 offEurlarAngle;
        [SerializeField] private Vector3 onEurlarAngle;

        public bool isOn;

        private void Update() {
            pivot.localRotation = Quaternion.Lerp(pivot.localRotation, isOn ? Quaternion.Euler(onEurlarAngle) : Quaternion.Euler(offEurlarAngle), Time.deltaTime * 5f);
        }
    }
}